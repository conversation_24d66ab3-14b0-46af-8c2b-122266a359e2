# Testing with GitLab Runner Locally

This guide shows how to test your GitLab CI pipeline locally using GitLab Runner.

## Prerequisites

1. **Docker** installed and running
2. **GitLab Runner** installed locally

## Install GitLab Runner

### On Ubuntu/Debian:
```bash
# Download and install GitLab Runner
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash
sudo apt-get install gitlab-runner
```

### On macOS:
```bash
# Using Homebrew
brew install gitlab-runner
```

### Using Docker:
```bash
# Run GitLab Runner in Docker
docker run -d --name gitlab-runner --restart always \
  -v /srv/gitlab-runner/config:/etc/gitlab-runner \
  -v /var/run/docker.sock:/var/run/docker.sock \
  gitlab/gitlab-runner:latest
```

## Test Individual Jobs

### Test JOOQ Generation Job

Create a test configuration file `.gitlab-ci-local.yml`:

```yaml
generate-jooq:
  stage: generate
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    - export MAVEN_OPTS="-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
    - until docker info; do sleep 1; done
  script:
    - echo "🔧 Generating JOOQ code using Testcontainers..."
    - ./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q
    - ./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q || echo "JOOQ generation completed"
    - |
      if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
        echo "✅ JOOQ code generated successfully"
        ls -la target/generated-sources/jooq/minaloc/mbaza/jooq/generated/
      else
        echo "❌ JOOQ code generation failed"
        exit 1
      fi
  artifacts:
    paths:
      - target/generated-sources/jooq/
```

Run the test:
```bash
gitlab-runner exec docker generate-jooq --docker-privileged
```

### Test Build Job

```bash
gitlab-runner exec docker build-dev-docker-image --docker-privileged
```

## Alternative: Use Act (GitHub Actions Runner)

If you want a simpler alternative, you can use `act` which simulates CI environments:

```bash
# Install act
curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash

# Create a simple workflow file for testing
mkdir -p .github/workflows
```

## Manual Step-by-Step Testing

If you prefer manual testing, follow these steps:

### 1. Test JOOQ Generation
```bash
# Clean start
./mvnw clean

# Test JOOQ generation
./generate-jooq.sh
```

### 2. Test Docker Build
```bash
# Build image
./mvnw spring-boot:build-image -Dspring-boot.build-image.imageName=test-api:latest

# Verify image
docker images | grep test-api
```

### 3. Test Image Run
```bash
# Run the image
docker run -p 8080:8080 test-api:latest

# In another terminal, test the application
curl http://localhost:8080/actuator/health
```

## Troubleshooting Local Tests

### Common Issues:

1. **Docker-in-Docker not working**:
   ```bash
   # Make sure Docker daemon is running
   sudo systemctl start docker
   
   # Check Docker info
   docker info
   ```

2. **Maven dependencies download slowly**:
   ```bash
   # Pre-download dependencies
   ./mvnw dependency:go-offline
   ```

3. **Testcontainers fails**:
   ```bash
   # Check if you can pull PostgreSQL image
   docker pull postgres:16.4-alpine3.20
   ```

4. **Memory issues**:
   ```bash
   # Increase Docker memory limit
   # Docker Desktop: Settings > Resources > Memory
   ```

## Expected Results

When everything works correctly, you should see:

1. **JOOQ Generation**: 
   - PostgreSQL container starts
   - Flyway migrations apply
   - JOOQ files generated in `target/generated-sources/jooq/`

2. **Docker Build**:
   - Spring Boot image builds successfully
   - Image appears in `docker images`

3. **Application Start**:
   - Container starts without errors
   - Application responds to health checks

## Performance Expectations

Local testing times (approximate):
- **JOOQ Generation**: 2-4 minutes
- **Docker Build**: 3-6 minutes  
- **Total Pipeline**: 5-10 minutes

These times will be similar in GitLab CI, possibly faster with runner caching.
