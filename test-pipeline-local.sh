#!/bin/bash

# Simple Local Pipeline Test
# This script runs GitLab CI jobs locally using gitlab-runner exec

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if GitLab Runner is available
if ! command -v gitlab-runner &> /dev/null; then
    print_info "GitLab Runner not found. Installing..."
    
    # Install GitLab Runner
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -L --output /tmp/gitlab-runner "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-linux-amd64"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        curl -L --output /tmp/gitlab-runner "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-darwin-amd64"
    else
        print_error "Unsupported OS. Please install GitLab Runner manually."
        exit 1
    fi
    
    chmod +x /tmp/gitlab-runner
    sudo mv /tmp/gitlab-runner /usr/local/bin/
    print_success "GitLab Runner installed"
fi

# Check Docker
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_header "LOCAL GITLAB CI PIPELINE TEST"

echo "This will run your GitLab CI pipeline locally using gitlab-runner exec"
echo "Jobs will be executed using the local configuration (.gitlab-ci-local.yml)"
echo ""

# Function to run a job
run_local_job() {
    local job_name=$1
    local config_file=${2:-.gitlab-ci-local.yml}
    
    print_info "Running job: $job_name"
    
    gitlab-runner exec docker \
        --docker-privileged \
        --docker-volumes "/var/run/docker.sock:/var/run/docker.sock" \
        --docker-volumes "$(pwd):$(pwd)" \
        --gitlab-ci-yml "$config_file" \
        "$job_name"
}

# Stage 1: Generate JOOQ
print_header "STAGE 1: JOOQ GENERATION"

if run_local_job "generate-jooq"; then
    print_success "JOOQ generation completed successfully"
    
    # Check artifacts
    if [ -d "target/generated-sources/jooq" ]; then
        print_success "JOOQ artifacts created"
        echo "Generated files:"
        find target/generated-sources/jooq -name "*.java" | head -5
        echo "... and more"
    fi
else
    print_error "JOOQ generation failed"
    exit 1
fi

echo ""
read -p "Continue to build stage? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Stopping after JOOQ generation"
    exit 0
fi

# Stage 2: Build Docker Image
print_header "STAGE 2: DOCKER IMAGE BUILD"

if run_local_job "build-local-docker-image"; then
    print_success "Docker image build completed successfully"
    
    # Show created images
    echo ""
    echo "Created Docker images:"
    docker images | grep local-test-api || echo "No images found"
else
    print_error "Docker image build failed"
    exit 1
fi

echo ""
read -p "Continue to test stage? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Stopping after Docker build"
    exit 0
fi

# Stage 3: Test Docker Image
print_header "STAGE 3: DOCKER IMAGE TEST"

if run_local_job "test-docker-image"; then
    print_success "Docker image test completed successfully"
else
    print_error "Docker image test failed"
    exit 1
fi

# Final summary
print_header "PIPELINE COMPLETE"

print_success "🎉 Local GitLab CI pipeline completed successfully!"
echo ""
echo "Summary:"
echo "✅ JOOQ code generation: PASSED"
echo "✅ Docker image build: PASSED"
echo "✅ Docker image test: PASSED"
echo ""
echo "Your pipeline is ready for GitLab CI/CD!"
echo ""
echo "🧹 Cleanup commands:"
echo "  docker image prune -f"
echo "  rm -rf target/"
echo "  rm -rf .m2/"
