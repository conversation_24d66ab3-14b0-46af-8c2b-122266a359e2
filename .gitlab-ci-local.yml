# Local GitLab CI Configuration
# This is a simplified version for local testing with gitlab-runner exec

variables:
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

# JOOQ code generation job
generate-jooq:
  stage: generate
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    # Install Java 24 and Maven
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    # Wait for Docker daemon to be ready
    - until docker info; do sleep 1; done
    # Create cache directory
    - mkdir -p .m2/repository
  script:
    - echo "🔧 Generating JOOQ code using Testcontainers..."
    - ./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q
    - ./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q || echo "JOOQ generation completed (ignoring Maven interruption)"
    # Verify JOOQ code was generated
    - |
      if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
        echo "✅ JOOQ code generated successfully"
        echo "Generated files:"
        ls -la target/generated-sources/jooq/minaloc/mbaza/jooq/generated/ | head -10
      else
        echo "❌ JOOQ code generation failed"
        exit 1
      fi
  artifacts:
    paths:
      - target/generated-sources/jooq/
    expire_in: 1 hour

# Build Docker image job (simplified for local testing)
build-local-docker-image:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  dependencies:
    - generate-jooq
  before_script:
    # Install Java 24 and Maven
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    # Wait for Docker daemon to be ready
    - until docker info; do sleep 1; done
    # Create cache directory
    - mkdir -p .m2/repository
  script:
    # Verify JOOQ code exists from previous stage
    - |
      if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
        echo "✅ Using JOOQ code from previous stage"
      else
        echo "❌ JOOQ code not found from previous stage"
        exit 1
      fi
    # Build Docker image with generated code (don't clean to preserve generated JOOQ code, skip tests)
    - echo "🐳 Building Docker image..."
    - export IMAGE_NAME="local-test-api:$(date +%s)"
    - ./mvnw spring-boot:build-image -Dspring-boot.build-image.imageName="$IMAGE_NAME" -DskipTests
    - echo "✅ Docker image built successfully: $IMAGE_NAME"
    - docker images | grep local-test-api

# Test the built image
test-docker-image:
  stage: test
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  dependencies:
    - build-local-docker-image
  before_script:
    - until docker info; do sleep 1; done
  script:
    - echo "🧪 Testing the built Docker image..."
    # Find the latest built image
    - export IMAGE_NAME=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep local-test-api | head -1)
    - echo "Testing image: $IMAGE_NAME"
    # Start container with environment file if available
    - |
      if [ -f ".env" ]; then
        echo "📄 Using .env file for container environment"
        CONTAINER_ID=$(docker run -d -p 8080:8080 --env-file .env "$IMAGE_NAME")
      else
        echo "⚠️  No .env file found, running without environment variables"
        CONTAINER_ID=$(docker run -d -p 8080:8080 "$IMAGE_NAME")
      fi
    - echo "Container started: $CONTAINER_ID"
    # Wait for application to start
    - echo "⏳ Waiting for application to start..."
    - sleep 30
    # Check if container is still running
    - |
      if docker ps | grep -q "$CONTAINER_ID"; then
        echo "✅ Container is running"
        # Test health endpoint
        echo "🔍 Testing health endpoint..."
        if wget -q --spider http://localhost:8080/actuator/health; then
          echo "✅ Health endpoint is responding"
        else
          echo "⚠️  Health endpoint not responding (may need more time or database)"
          echo "Container logs:"
          docker logs --tail 20 "$CONTAINER_ID"
        fi
      else
        echo "❌ Container stopped unexpectedly"
        echo "Container logs:"
        docker logs "$CONTAINER_ID"
        exit 1
      fi
    # Cleanup
    - echo "🧹 Cleaning up test container..."
    - docker stop "$CONTAINER_ID" || true
    - docker rm "$CONTAINER_ID" || true
    - echo "✅ Test completed successfully"
