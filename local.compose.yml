services:
  valkey:
    image: docker.io/bitnami/valkey:8.0
    environment:
      ALLOW_EMPTY_PASSWORD: yes
    ports:
      - "6379:6379"
    volumes:
      - valkey_data:/bitnami/valkey/data

  postgres:
    image: postgres:16.4-alpine3.20
    container_name: esoko-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_MULTIPLE_DATABASES: mbaza
    ports:
      - "5433:5432"
    volumes:
      - db-data:/var/lib/postgresql/data
    entrypoint: [ "docker-entrypoint.sh", "postgres" ]

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z-cpuv1
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data

volumes:
  valkey_data:

  db-data:

  minio-data:
