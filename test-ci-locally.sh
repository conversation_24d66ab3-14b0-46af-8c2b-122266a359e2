#!/bin/bash

# Local CI Pipeline Simulation Script
# This script simulates the GitLab CI pipeline locally using Docker

set -e  # Exit on any error

echo "🧪 Starting Local CI Pipeline Simulation..."
echo "This simulates the GitLab CI pipeline on your local machine"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_stage() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 STAGE: $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check prerequisites
print_stage "PREREQUISITES CHECK"

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

if ! command -v ./mvnw &> /dev/null; then
    print_error "Maven wrapper (mvnw) not found"
    exit 1
fi

print_success "All prerequisites met"
echo ""

# Stage 1: Generate JOOQ Code (simulating generate-jooq job)
print_stage "GENERATE - JOOQ Code Generation"

echo "📦 Compiling test classes..."
if ./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q; then
    print_success "Test classes compiled successfully"
else
    print_error "Failed to compile test classes"
    exit 1
fi

echo "🔧 Running JOOQ code generation..."
if ./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q 2>/dev/null || true; then
    # Check if JOOQ code was generated (ignore Maven exit code)
    if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated 2>/dev/null)" ]; then
        print_success "JOOQ code generated successfully"
        echo "Generated files:"
        ls -la target/generated-sources/jooq/minaloc/mbaza/jooq/generated/ | head -10
        if [ $(ls -1 target/generated-sources/jooq/minaloc/mbaza/jooq/generated/ | wc -l) -gt 10 ]; then
            echo "... and more files"
        fi
    else
        print_error "JOOQ code generation failed"
        exit 1
    fi
else
    print_error "JOOQ generation process failed"
    exit 1
fi

echo ""

# Stage 2: Build Docker Image (simulating build-docker-image job)
print_stage "BUILD - Docker Image Creation"

echo "🔍 Verifying JOOQ code exists..."
if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
    print_success "JOOQ code verified from previous stage"
else
    print_error "JOOQ code not found from previous stage"
    exit 1
fi

echo "🐳 Building Docker image..."
IMAGE_NAME="local-test-api:$(date +%s)"

if ./mvnw spring-boot:build-image -Dspring-boot.build-image.imageName="$IMAGE_NAME" -DskipTests -q; then
    print_success "Docker image built successfully: $IMAGE_NAME"
else
    print_error "Docker image build failed"
    exit 1
fi

echo ""

# Stage 3: Test the Built Image (simulating deployment verification)
print_stage "TEST - Docker Image Verification"

echo "🧪 Testing the built Docker image..."

# Check if image exists
if docker images | grep -q "local-test-api"; then
    print_success "Docker image exists in local registry"
else
    print_error "Docker image not found in local registry"
    exit 1
fi

# Try to run the image (just to see if it starts)
echo "🚀 Testing image startup (will stop after 10 seconds)..."
CONTAINER_ID=$(docker run -d -p 8080:8080 "$IMAGE_NAME")

if [ $? -eq 0 ]; then
    print_success "Container started successfully: $CONTAINER_ID"
    
    # Wait a bit and check if container is still running
    sleep 5
    if docker ps | grep -q "$CONTAINER_ID"; then
        print_success "Container is running healthy"
        
        # Try to check if the application responds (optional)
        echo "🔍 Checking application health..."
        sleep 5
        if curl -f http://localhost:8080/actuator/health 2>/dev/null || curl -f http://localhost:8080 2>/dev/null; then
            print_success "Application is responding to HTTP requests"
        else
            print_warning "Application may still be starting up (this is normal)"
        fi
    else
        print_warning "Container stopped unexpectedly (check logs with: docker logs $CONTAINER_ID)"
    fi
    
    # Stop and remove the test container
    echo "🧹 Cleaning up test container..."
    docker stop "$CONTAINER_ID" >/dev/null 2>&1
    docker rm "$CONTAINER_ID" >/dev/null 2>&1
    print_success "Test container cleaned up"
else
    print_error "Failed to start container"
    exit 1
fi

echo ""

# Final Summary
print_stage "SUMMARY"

print_success "🎉 Local CI Pipeline Simulation Completed Successfully!"
echo ""
echo "✅ JOOQ code generation: PASSED"
echo "✅ Docker image build: PASSED"
echo "✅ Image verification: PASSED"
echo ""
echo "🚀 Your pipeline is ready for GitLab CI/CD!"
echo ""
echo "Next steps:"
echo "1. Commit and push your changes to GitLab"
echo "2. Monitor the GitLab CI pipeline"
echo "3. The pipeline should work exactly like this local test"
echo ""
echo "Built image: $IMAGE_NAME"
echo "To clean up the test image: docker rmi $IMAGE_NAME"
