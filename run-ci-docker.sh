#!/bin/bash

# Docker-based CI Pipeline Simulation
# This script runs your GitLab CI pipeline steps using Docker containers
# No GitLab Runner required - pure Docker approach

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check Docker
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_header "DOCKER-BASED CI PIPELINE SIMULATION"

echo "This simulates your GitLab CI pipeline using Docker containers"
echo "Each stage runs in the same Docker environment as GitLab CI"
echo ""

# Create a shared volume for artifacts
VOLUME_NAME="ci-artifacts-$(date +%s)"
docker volume create "$VOLUME_NAME"

print_success "Created shared volume: $VOLUME_NAME"

# Function to run CI stage in Docker
run_ci_stage() {
    local stage_name=$1
    local docker_image=$2
    shift 2
    local commands=("$@")
    
    print_info "Running stage: $stage_name"
    
    # Create a temporary script with all commands
    local script_file="/tmp/ci-stage-$stage_name.sh"
    cat > "$script_file" << 'EOF'
#!/bin/bash
set -e

# Install Java 24 and Maven
apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing

# Wait for Docker daemon to be ready
until docker info; do sleep 1; done

# Set up environment
export MAVEN_OPTS="-Dmaven.repo.local=.m2/repository"
mkdir -p .m2/repository

# Change to project directory
cd /project

EOF
    
    # Add the commands to the script
    for cmd in "${commands[@]}"; do
        echo "$cmd" >> "$script_file"
    done
    
    # Make script executable
    chmod +x "$script_file"
    
    # Run the stage in Docker
    docker run --rm \
        --privileged \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v "$(pwd):/project" \
        -v "$VOLUME_NAME:/artifacts" \
        -v "$script_file:/run-stage.sh" \
        -w /project \
        "$docker_image" \
        /run-stage.sh
    
    # Clean up
    rm -f "$script_file"
}

# Stage 1: Generate JOOQ
print_header "STAGE 1: JOOQ GENERATION"

jooq_commands=(
    "echo '🔧 Generating JOOQ code using Testcontainers...'"
    "./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q"
    "./mvnw exec:java -Dexec.mainClass='minaloc.mbaza.api.codegen.TestcontainersJooqCodegen' -Dexec.classpathScope='test' -q || echo 'JOOQ generation completed (ignoring Maven interruption)'"
    "if [ -d 'target/generated-sources/jooq/minaloc/mbaza/jooq/generated' ] && [ \"\$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)\" ]; then"
    "  echo '✅ JOOQ code generated successfully'"
    "  echo 'Generated files:'"
    "  ls -la target/generated-sources/jooq/minaloc/mbaza/jooq/generated/ | head -10"
    "  # Copy artifacts to shared volume"
    "  cp -r target/generated-sources /artifacts/"
    "else"
    "  echo '❌ JOOQ code generation failed'"
    "  exit 1"
    "fi"
)

if run_ci_stage "generate-jooq" "docker:24.0.5-dind" "${jooq_commands[@]}"; then
    print_success "JOOQ generation stage completed"
else
    print_error "JOOQ generation stage failed"
    docker volume rm "$VOLUME_NAME" 2>/dev/null || true
    exit 1
fi

echo ""
read -p "Continue to build stage? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Stopping after JOOQ generation"
    docker volume rm "$VOLUME_NAME" 2>/dev/null || true
    exit 0
fi

# Stage 2: Build Docker Image
print_header "STAGE 2: DOCKER IMAGE BUILD"

build_commands=(
    "echo '🐳 Building Docker image...'"
    "# Restore artifacts from previous stage"
    "if [ -d '/artifacts/generated-sources' ]; then"
    "  mkdir -p target"
    "  cp -r /artifacts/generated-sources target/"
    "  echo '✅ Restored JOOQ artifacts from previous stage'"
    "else"
    "  echo '❌ JOOQ artifacts not found'"
    "  exit 1"
    "fi"
    "# Verify JOOQ code exists"
    "if [ -d 'target/generated-sources/jooq/minaloc/mbaza/jooq/generated' ] && [ \"\$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)\" ]; then"
    "  echo '✅ JOOQ code verified'"
    "else"
    "  echo '❌ JOOQ code verification failed'"
    "  exit 1"
    "fi"
    "# Build Docker image"
    "export IMAGE_NAME='local-ci-test:latest'"
    "./mvnw spring-boot:build-image -Dspring-boot.build-image.imageName=\"\$IMAGE_NAME\" -DskipTests"
    "echo '✅ Docker image built: '\$IMAGE_NAME"
    "docker images | grep local-ci-test || echo 'Image not found in listing'"
)

if run_ci_stage "build-image" "docker:24.0.5-dind" "${build_commands[@]}"; then
    print_success "Docker build stage completed"
else
    print_error "Docker build stage failed"
    docker volume rm "$VOLUME_NAME" 2>/dev/null || true
    exit 1
fi

echo ""
read -p "Continue to test stage? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Stopping after Docker build"
    docker volume rm "$VOLUME_NAME" 2>/dev/null || true
    exit 0
fi

# Stage 3: Test Docker Image
print_header "STAGE 3: DOCKER IMAGE TEST"

test_commands=(
    "echo '🧪 Testing Docker image...'"
    "# Find the built image"
    "export IMAGE_NAME='local-ci-test:latest'"
    "if docker images | grep -q 'local-ci-test'; then"
    "  echo '✅ Found Docker image: '\$IMAGE_NAME"
    "else"
    "  echo '❌ Docker image not found'"
    "  exit 1"
    "fi"
    "# Test container startup"
    "echo '🚀 Starting test container...'"
    "if [ -f '/project/.env' ]; then"
    "  CONTAINER_ID=\$(docker run -d -p 8080:8080 --env-file /project/.env \"\$IMAGE_NAME\")"
    "else"
    "  CONTAINER_ID=\$(docker run -d -p 8080:8080 \"\$IMAGE_NAME\")"
    "fi"
    "echo 'Container started: '\$CONTAINER_ID"
    "# Wait and test"
    "sleep 30"
    "if docker ps | grep -q \"\$CONTAINER_ID\"; then"
    "  echo '✅ Container is running'"
    "  # Try health check"
    "  if wget -q --spider http://localhost:8080/actuator/health 2>/dev/null; then"
    "    echo '✅ Health endpoint responding'"
    "  else"
    "    echo '⚠️  Health endpoint not responding (may need more time)'"
    "  fi"
    "else"
    "  echo '❌ Container stopped unexpectedly'"
    "  docker logs \"\$CONTAINER_ID\" || true"
    "fi"
    "# Cleanup"
    "docker stop \"\$CONTAINER_ID\" 2>/dev/null || true"
    "docker rm \"\$CONTAINER_ID\" 2>/dev/null || true"
    "echo '✅ Test completed'"
)

if run_ci_stage "test-image" "docker:24.0.5-dind" "${test_commands[@]}"; then
    print_success "Docker test stage completed"
else
    print_error "Docker test stage failed"
    docker volume rm "$VOLUME_NAME" 2>/dev/null || true
    exit 1
fi

# Cleanup
print_header "CLEANUP"
docker volume rm "$VOLUME_NAME" 2>/dev/null || true
print_success "Cleaned up shared volume"

# Final summary
print_header "PIPELINE COMPLETE"

print_success "🎉 Docker-based CI pipeline completed successfully!"
echo ""
echo "Summary:"
echo "✅ JOOQ code generation: PASSED"
echo "✅ Docker image build: PASSED"
echo "✅ Docker image test: PASSED"
echo ""
echo "Your pipeline is ready for GitLab CI/CD!"
echo ""
echo "🧹 Cleanup commands:"
echo "  docker image prune -f"
echo "  docker system prune -f"
