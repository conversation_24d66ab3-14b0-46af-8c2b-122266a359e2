# GitLab Runner Setup Guide

This guide shows how to set up a real GitLab Runner to test your CI/CD pipeline locally before deploying to production.

## Why Use a Real GitLab Runner?

- **Exact CI Environment**: Tests the exact same environment as production
- **Docker-in-Docker Support**: Proper Testcontainers support
- **Real Pipeline Testing**: Tests the complete `.gitlab-ci.yml` configuration
- **Debugging**: Better debugging capabilities with real GitLab Runner logs

## Prerequisites

- Docker installed and running
- GitLab project with admin access
- Linux/macOS/Windows machine with sufficient resources (4GB+ RAM recommended)

## Installation

### Option 1: Docker-based GitLab Runner (Recommended)

```bash
# Create a volume for GitLab Runner configuration
docker volume create gitlab-runner-config

# Run GitLab Runner in Docker
docker run -d --name gitlab-runner --restart always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v gitlab-runner-config:/etc/gitlab-runner \
  gitlab/gitlab-runner:latest
```

### Option 2: Native Installation

#### Ubuntu/Debian:
```bash
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash
sudo apt-get install gitlab-runner
```

#### CentOS/RHEL:
```bash
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.rpm.sh" | sudo bash
sudo yum install gitlab-runner
```

#### macOS:
```bash
brew install gitlab-runner
```

## Registration

### Step 1: Get Registration Token

1. Go to your GitLab project
2. Navigate to **Settings** → **CI/CD**
3. Expand **Runners** section
4. Copy the **Registration token**

### Step 2: Register the Runner

#### For Docker-based Runner:
```bash
docker run --rm -it -v gitlab-runner-config:/etc/gitlab-runner \
  gitlab/gitlab-runner:latest register \
  --non-interactive \
  --url "https://gitlab.com/" \
  --registration-token "YOUR_REGISTRATION_TOKEN" \
  --executor "docker" \
  --docker-image alpine:latest \
  --description "Local Docker Runner" \
  --tag-list "local,docker" \
  --docker-privileged \
  --docker-volumes "/var/run/docker.sock:/var/run/docker.sock"
```

#### For Native Installation:
```bash
sudo gitlab-runner register \
  --non-interactive \
  --url "https://gitlab.com/" \
  --registration-token "YOUR_REGISTRATION_TOKEN" \
  --executor "docker" \
  --docker-image alpine:latest \
  --description "Local Docker Runner" \
  --tag-list "local,docker" \
  --docker-privileged \
  --docker-volumes "/var/run/docker.sock:/var/run/docker.sock"
```

### Step 3: Configure for Testcontainers

Edit the runner configuration to ensure Docker-in-Docker works properly:

#### For Docker-based Runner:
```bash
# Edit the configuration
docker exec -it gitlab-runner nano /etc/gitlab-runner/config.toml
```

#### For Native Installation:
```bash
sudo nano /etc/gitlab-runner/config.toml
```

Update the configuration:
```toml
[[runners]]
  name = "Local Docker Runner"
  url = "https://gitlab.com/"
  token = "YOUR_TOKEN"
  executor = "docker"
  [runners.docker]
    tls_verify = false
    image = "alpine:latest"
    privileged = true
    disable_entrypoint_overwrite = false
    oom_kill_disable = false
    disable_cache = false
    volumes = ["/var/run/docker.sock:/var/run/docker.sock", "/cache"]
    shm_size = 0
```

## Testing Your Pipeline

### Step 1: Update .gitlab-ci.yml for Local Testing

Add tags to your jobs to run on your local runner:

```yaml
generate-jooq:
  stage: generate
  tags:
    - local  # This will run on your local runner
  # ... rest of configuration
```

### Step 2: Push and Test

```bash
# Commit your changes
git add .
git commit -m "Test CI with local runner"
git push origin your-branch

# Monitor the pipeline in GitLab UI or via CLI
```

### Step 3: Monitor Logs

#### Docker-based Runner:
```bash
# View runner logs
docker logs -f gitlab-runner

# View specific job logs in GitLab UI
```

#### Native Installation:
```bash
# View runner logs
sudo journalctl -u gitlab-runner -f

# Or check runner logs directly
sudo gitlab-runner --debug run
```

## Environment Variables for Local Testing

Create a `.env.local` file for local testing:

```bash
# .env.local - Local testing environment
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=mbaza
DATABASE_PORT=5432
DATABASE_HOST=localhost
SHOW_SQL=true

# Security (use test values)
SECURITY_JWT_SECRET_KEY=test-secret-key-for-local-development-only
SECURITY_JWT_EXPIRATION_TIME=86400000

# Admin (test credentials)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=testpassword
```

## Troubleshooting

### Common Issues:

1. **Docker-in-Docker not working**:
   ```bash
   # Ensure privileged mode is enabled
   # Check if /var/run/docker.sock is mounted
   docker exec gitlab-runner docker info
   ```

2. **Runner not picking up jobs**:
   ```bash
   # Check runner status
   docker exec gitlab-runner gitlab-runner list
   
   # Restart runner
   docker restart gitlab-runner
   ```

3. **Testcontainers failing**:
   ```bash
   # Check Docker daemon access
   docker exec gitlab-runner docker pull postgres:16.4-alpine3.20
   ```

4. **Memory issues**:
   ```bash
   # Increase Docker memory limit
   # For Docker Desktop: Settings → Resources → Memory (4GB+)
   ```

### Debug Commands:

```bash
# Check runner configuration
docker exec gitlab-runner cat /etc/gitlab-runner/config.toml

# Test Docker access from runner
docker exec gitlab-runner docker ps

# View detailed runner logs
docker logs gitlab-runner --tail 100

# Test a simple job
docker exec gitlab-runner gitlab-runner exec docker test-job
```

## Performance Optimization

### Runner Configuration:
```toml
[[runners]]
  # ... other settings
  [runners.docker]
    # Use local cache
    volumes = ["/var/run/docker.sock:/var/run/docker.sock", "/cache", "/tmp/cache:/tmp/cache"]
    
    # Increase shared memory
    shm_size = 268435456  # 256MB
    
    # Use faster filesystem
    disable_cache = false
```

### Pipeline Optimization:
```yaml
# Add caching to your jobs
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .m2/repository/
    - target/
```

## Security Considerations

- **Local Runner**: Only use for development/testing
- **Secrets**: Don't use production secrets in local testing
- **Network**: Ensure runner can't access production systems
- **Cleanup**: Regularly clean up Docker images and containers

## Next Steps

1. **Test JOOQ Generation**: Run the `generate-jooq` job locally
2. **Test Full Pipeline**: Run complete build and test pipeline
3. **Debug Issues**: Use runner logs to identify and fix issues
4. **Optimize Performance**: Tune runner configuration for your needs
5. **Production Deployment**: Once tested, deploy to production runners

## Useful Commands

```bash
# Start/stop runner
docker start gitlab-runner
docker stop gitlab-runner

# Update runner
docker pull gitlab/gitlab-runner:latest
docker stop gitlab-runner
docker rm gitlab-runner
# Re-run the installation command

# Clean up
docker system prune -f
docker volume prune -f
```
