# Application
SERVER_PORT=8080

# Database
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=mbaza
DATABASE_PORT=5433
DATABASE_HOST=localhost
SHOW_SQL=false

# Security
SECURITY_JWT_SECRET_KEY=oczBdAvplFuiCFi7YI+ZnvjB6l1zPpzaFQsqKlUurqIqW0yE0fsMaPmlY0bLlHJAQI0SPe9BVQj6xF2Y9OKCJQ==
SECURITY_JWT_EXPIRATION_TIME=86400000

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4000, http://localhost:5000,http://localhost:6000,http://localhost:7000,http://localhost:8000

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=password

MINIO_URL=http://localhost:9000
MINIO_ACCESS_KEY=oczBdAvplFuiCFi7YI+ZnvjB6l1zPpzaFQsqKlUurqIqW0yE0f
MINIO_SECRET_KEY=oczBdAvplFuiCFi7YI+ZnvjB6l1zPpzaFQsqKlUurqIqW0yE0f
MINIO_BUCKET_NAME=evidence