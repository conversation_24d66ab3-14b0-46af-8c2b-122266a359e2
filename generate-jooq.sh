#!/bin/bash

# Script to generate JOOQ code using Testcontainers
# This script compiles and runs the TestcontainersJooqCodegen class directly

echo "🚀 Starting JOOQ code generation using Testcontainers..."
echo "This will:"
echo "  1. Start a PostgreSQL container using Testcontainers"
echo "  2. Apply Flyway migrations to the container"
echo "  3. Generate JOOQ code from the migrated schema"
echo "  4. Clean up the container"
echo ""

# Create target directory if it doesn't exist
mkdir -p target/generated-sources/jooq

# First, compile the test classes (including our code generator)
echo "📦 Compiling test classes..."
./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q

if [ $? -ne 0 ]; then
    echo "❌ Failed to compile test classes!"
    exit 1
fi

# Run the JOOQ code generator
echo "🔧 Running JOOQ code generation..."
./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q

# Check if JOOQ files were generated (ignore Maven exit code due to interruption issue)
if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
    echo ""
    echo "✅ JOOQ code generation completed successfully!"
    echo "Generated code is available in: target/generated-sources/jooq"
    echo ""
    echo "To use the generated code in your IDE, you may need to:"
    echo "  1. Refresh your project"
    echo "  2. Reimport Maven dependencies"
    echo "  3. Mark target/generated-sources/jooq as a source folder"
    echo ""
    echo "🔨 Testing compilation with generated code..."
    ./mvnw compile -q
    if [ $? -eq 0 ]; then
        echo "✅ Main project compiles successfully with generated JOOQ code!"
    else
        echo "⚠️  Main project compilation failed. You may need to refresh your IDE."
    fi
else
    echo ""
    echo "❌ JOOQ code generation failed!"
    echo "Please check the error messages above."
    exit 1
fi
