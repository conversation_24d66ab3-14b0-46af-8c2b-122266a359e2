#!/bin/bash

# Script to generate JOOQ code using Testcontainers
# This script runs the Maven profile that uses Testcontainers to spin up a PostgreSQL container,
# applies Flyway migrations, and generates JOOQ code from the migrated schema.

echo "🚀 Starting JOOQ code generation using Testcontainers..."
echo "This will:"
echo "  1. Start a PostgreSQL container using Testcontainers"
echo "  2. Apply Flyway migrations to the container"
echo "  3. Generate JOOQ code from the migrated schema"
echo "  4. Clean up the container"
echo ""

# Run Maven with the jooq-testcontainers profile
mvn clean compile -Pjooq-testcontainers

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ JOOQ code generation completed successfully!"
    echo "Generated code is available in: target/generated-sources/jooq"
    echo ""
    echo "To use the generated code in your IDE, you may need to:"
    echo "  1. Refresh your project"
    echo "  2. Reimport Maven dependencies"
    echo "  3. Mark target/generated-sources/jooq as a source folder"
else
    echo ""
    echo "❌ JOOQ code generation failed!"
    echo "Please check the error messages above."
    exit 1
fi
