# GitLab CI/CD Pipeline Setup

This document explains the GitLab CI/CD pipeline configuration for the Spring Boot API with JOOQ code generation using Testcontainers.

## Pipeline Overview

The pipeline consists of three stages:

### 1. **Generate Stage** (`generate-jooq`)
- **Purpose**: Generate JOOQ code using Testcontainers
- **Process**:
  1. Starts a PostgreSQL container using Testcontainers
  2. Applies Flyway migrations to create the latest schema
  3. Generates JOOQ code from the migrated schema
  4. Stores generated code as artifacts for the next stage
- **Duration**: ~3-5 minutes
- **Artifacts**: Generated JOOQ code in `target/generated-sources/jooq/`

### 2. **Build Stage** (`build-*-docker-image`)
- **Purpose**: Build Docker image with generated JOOQ code
- **Process**:
  1. Downloads artifacts from the generate stage
  2. Verifies JOOQ code exists
  3. Builds Spring Boot Docker image using Cloud Native Buildpacks
  4. Pushes image to GitLab Container Registry
- **Duration**: ~5-10 minutes

### 3. **Deploy Stage** (`deploy-to-*-environment`)
- **Purpose**: Deploy the built image to target environment
- **Process**:
  1. Connects to target server via SSH
  2. Pulls the latest Docker image
  3. Updates Docker Compose configuration
  4. Restarts the application
- **Duration**: ~2-3 minutes

## Key Features

### ✅ **Testcontainers Integration**
- No hardcoded database connections in CI/CD
- Isolated and reproducible JOOQ generation
- Always uses latest Flyway migrations

### ✅ **Caching & Performance**
- Maven dependencies cached between builds
- Generated JOOQ code passed as artifacts
- Docker layer caching for faster builds

### ✅ **Reliability**
- Automatic retries for system failures
- Timeouts to prevent hanging jobs
- Comprehensive error checking

### ✅ **Multi-Environment Support**
- Development (`dev` branch)
- Staging (`release/*` and `hotfix/*` branches)
- Production (`main` branch)

## Environment Variables Required

### Docker Registry
- `CI_REGISTRY_IMAGE`: GitLab container registry URL (auto-provided)
- `CI_JOB_TOKEN`: GitLab job token (auto-provided)

### Development Environment
- `DEV_PRIVATE_KEY`: SSH private key for dev server
- `DEV_PUBLIC_IP_ADDRESS`: Dev server IP address
- `DEV_USER`: SSH username for dev server
- `DEV_SERVER_PATH`: Deployment path on dev server
- `DEV_PROJECT_NAME`: Docker Compose project name for dev
- `dev_env`: Environment file content for dev

### Staging Environment
- `STG_PRIVATE_KEY`: SSH private key for staging server
- `STG_PUBLIC_IP_ADDRESS`: Staging server IP address
- `STG_USER`: SSH username for staging server
- `STG_SERVER_PATH`: Deployment path on staging server
- `STG_PROJECT_NAME`: Docker Compose project name for staging
- `stg_env`: Environment file content for staging

### Production Environment
- `PROD_PRIVATE_KEY`: SSH private key for production server
- `PROD_PUBLIC_IP_ADDRESS`: Production server IP address
- `PROD_USER`: SSH username for production server
- `PROD_SERVER_PATH`: Deployment path on production server
- `PROD_PROJECT_NAME`: Docker Compose project name for production
- `prod_env`: Environment file content for production

## Branch Strategy

| Branch Pattern | Triggers | Environment |
|---------------|----------|-------------|
| `dev` | Generate + Build + Deploy | Development |
| `release/*` | Generate + Build + Deploy | Staging |
| `hotfix/*` | Generate + Build + Deploy | Staging |
| `main` | Generate + Build + Deploy | Production |

## Troubleshooting

### JOOQ Generation Fails
- Check Docker daemon is available in runner
- Verify Flyway migrations are valid
- Check Testcontainers can pull PostgreSQL image

### Build Fails
- Ensure JOOQ artifacts are available from previous stage
- Check Java 24 and Maven installation
- Verify Docker registry permissions

### Deployment Fails
- Check SSH key permissions and server access
- Verify environment variables are set correctly
- Ensure target server has Docker and Docker Compose

## Local Testing

To test the JOOQ generation locally:
```bash
./generate-jooq.sh
```

To test the full build locally:
```bash
./mvnw clean spring-boot:build-image
```

## Performance Optimization

The pipeline includes several optimizations:
- **Parallel Execution**: Generate and build stages can run in parallel for different branches
- **Artifact Caching**: Generated JOOQ code is cached between stages
- **Maven Caching**: Dependencies are cached between builds
- **Docker Layer Caching**: Docker images use layer caching for faster builds

## Security Considerations

- SSH private keys are stored as protected GitLab CI variables
- Docker registry authentication uses GitLab job tokens
- Environment files are stored as protected variables
- No database credentials are exposed in the pipeline
