package minaloc.mbaza.api.codegen;

import org.flywaydb.core.Flyway;
import org.jooq.codegen.GenerationTool;
import org.jooq.meta.jaxb.Configuration;
import org.jooq.meta.jaxb.Database;
import org.jooq.meta.jaxb.Generate;
import org.jooq.meta.jaxb.Generator;
import org.jooq.meta.jaxb.Jdbc;
import org.jooq.meta.jaxb.Logging;
import org.jooq.meta.jaxb.Strategy;
import org.jooq.meta.jaxb.Target;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

/**
 * Custom JOOQ code generator that uses Testcontainers to spin up a PostgreSQL container,
 * applies Flyway migrations, and then generates JOOQ code from the migrated schema.
 * 
 * This approach eliminates the need for hardcoded database connections in the build file
 * and ensures the generated code is always based on the latest migration files.
 */
public class TestcontainersJooqCodegen {

    private static final String POSTGRES_IMAGE = "postgres:16.4-alpine3.20";
    private static final String DATABASE_NAME = "mbaza";
    private static final String USERNAME = "postgres";
    private static final String PASSWORD = "postgres";

    public static void main(String[] args) {
        try {
            // Start PostgreSQL container
            try (PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse(POSTGRES_IMAGE))
                    .withDatabaseName(DATABASE_NAME)
                    .withUsername(USERNAME)
                    .withPassword(PASSWORD)) {

                postgres.start();

                String jdbcUrl = postgres.getJdbcUrl();
                System.out.println("Started PostgreSQL container at: " + jdbcUrl);

                // Apply Flyway migrations
                applyMigrations(jdbcUrl);

                // Generate JOOQ code
                generateJooqCode(jdbcUrl);

                System.out.println("JOOQ code generation completed successfully!");

                // Ensure clean shutdown
                postgres.stop();
            }

            // Force exit to avoid Maven exec plugin hanging
            System.exit(0);

        } catch (Exception e) {
            System.err.println("JOOQ code generation failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    private static void applyMigrations(String jdbcUrl) {
        System.out.println("Applying Flyway migrations...");
        
        Flyway flyway = Flyway.configure()
                .dataSource(jdbcUrl, USERNAME, PASSWORD)
                .locations("classpath:db/migration")
                .baselineOnMigrate(true)
                .baselineVersion("0")
                .outOfOrder(true)
                .validateOnMigrate(true)
                .load();
        
        flyway.migrate();
        System.out.println("Flyway migrations applied successfully!");
    }

    private static void generateJooqCode(String jdbcUrl) throws Exception {
        System.out.println("Generating JOOQ code...");
        
        Configuration configuration = new Configuration()
                .withLogging(Logging.WARN)
                .withJdbc(new Jdbc()
                        .withDriver("org.postgresql.Driver")
                        .withUrl(jdbcUrl)
                        .withUser(USERNAME)
                        .withPassword(PASSWORD))
                .withGenerator(new Generator()
                        .withName("org.jooq.codegen.JavaGenerator")
                        .withDatabase(new Database()
                                .withName("org.jooq.meta.postgres.PostgresDatabase")
                                .withInputSchema("public")
                                .withExcludes("flyway.*")
                                .withIncludeIndexes(false))
                        .withGenerate(new Generate()
                                .withDeprecated(false)
                                .withRecords(true)
                                .withImmutablePojos(true)
                                .withFluentSetters(true)
                                .withImplicitJoinPathsToOne(true))
                        .withTarget(new Target()
                                .withPackageName("minaloc.mbaza.jooq.generated")
                                .withDirectory("target/generated-sources/jooq"))
                        .withStrategy(new Strategy()
                                .withName("org.jooq.codegen.DefaultGeneratorStrategy")));

        GenerationTool.generate(configuration);
        System.out.println("JOOQ code generated successfully!");
    }
}
