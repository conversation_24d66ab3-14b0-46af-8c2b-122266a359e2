spring.application.name=api
server.port=${SERVER_PORT:8080}
application.security.allowed-origins=${ALLOWED_ORIGINS:}
# Database
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.url=jdbc:postgresql://${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}
# Hibernate
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false
# Security
security.jwt.secret-key=${SECURITY_JWT_SECRET_KEY}
security.jwt.expiration-time=${SECURITY_JWT_EXPIRATION_TIME}
# Swagger
springdoc.swagger-ui.doc-expansion=none
springdoc.swagger-ui.operations-sorter=method
springdoc.swagger-ui.persist-authorization=true
springdoc.swagger-ui.syntaxHighlight.theme=arta
springdoc.swagger-ui.tags-sorter=alpha
springdoc.use-fqn=true
server.forward-headers-strategy=framework
spring.output.ansi.enabled=ALWAYS
# Admin Credentials
application.users.admin.email=${ADMIN_EMAIL}
application.users.admin.password=${ADMIN_PASSWORD}
# Flyway migrations
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.out-of-order=true
spring.flyway.validate-on-migrate=true
# Minio Configuration
application.minio.url=${MINIO_URL}
application.minio.access-key=${MINIO_ACCESS_KEY}
application.minio.secret-key=${MINIO_SECRET_KEY}
application.minio.bucket-name=${MINIO_BUCKET_NAME}



