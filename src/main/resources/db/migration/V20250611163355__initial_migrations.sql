-- Core system tables
CREATE TABLE IF NOT EXISTS permissions (
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6) NOT NULL,
    updated_at  TIMESTAMP(6) NOT NULL,
    deleted_at  TIMESTAMP(6),
    name        VARCHAR(30) NOT NULL UNIQUE,
    description VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS roles (
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6) NOT NULL,
    updated_at  TIMESTAMP(6) NOT NULL,
    deleted_at  TIMESTAMP(6),
    name        VARCHAR(15) NOT NULL UNIQUE,
    description VARCHAR(50),
    valid_from  TIMESTAMP(6),
    valid_to    TIMESTAMP(6)
);

CREATE TABLE IF NOT EXISTS role_permissions (
    role_id       VARCHAR(50) NOT NULL,
    permission_id VARCHAR(50) NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    CONSTRAINT fk_role_permissions_role
        FOREIGN KEY (role_id) REFERENCES roles(id),
    CONSTRAINT fk_role_permissions_permission
        FOREIGN KEY (permission_id) REFERENCES permissions(id)
);

-- Geographic hierarchy tables
CREATE TABLE IF NOT EXISTS provinces (
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL,
    deleted_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL
);

CREATE TABLE IF NOT EXISTS districts (
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6) NOT NULL,
    updated_at  TIMESTAMP(6) NOT NULL,
    deleted_at  TIMESTAMP(6),
    code        VARCHAR(10),
    name        VARCHAR(20) NOT NULL,
    province_id VARCHAR(50) NOT NULL,
    CONSTRAINT fk_districts_province
        FOREIGN KEY (province_id) REFERENCES provinces(id)
);

CREATE TABLE IF NOT EXISTS sectors (
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6) NOT NULL,
    updated_at  TIMESTAMP(6) NOT NULL,
    deleted_at  TIMESTAMP(6),
    code        VARCHAR(10),
    name        VARCHAR(20) NOT NULL,
    district_id VARCHAR(50) NOT NULL,
    CONSTRAINT fk_sectors_district
        FOREIGN KEY (district_id) REFERENCES districts(id)
);

CREATE TABLE IF NOT EXISTS cells (
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL,
    deleted_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL,
    sector_id  VARCHAR(50) NOT NULL,
    CONSTRAINT fk_cells_sector
        FOREIGN KEY (sector_id) REFERENCES sectors(id)
);

CREATE TABLE IF NOT EXISTS villages (
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL,
    deleted_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL,
    cell_id    VARCHAR(50) NOT NULL,
    CONSTRAINT fk_villages_cell
        FOREIGN KEY (cell_id) REFERENCES cells(id)
);

-- Organization and user tables
CREATE TABLE IF NOT EXISTS organizations (
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL,
    deleted_at TIMESTAMP(6),
    name       VARCHAR(100) NOT NULL,
    is_public  BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS users (
    id                 VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at         TIMESTAMP(6) NOT NULL,
    updated_at         TIMESTAMP(6) NOT NULL,
    deleted_at         TIMESTAMP(6),
    verified_at        TIMESTAMP(6),
    username           VARCHAR(30) UNIQUE,
    email              VARCHAR(150) UNIQUE,
    password           VARCHAR(150) NOT NULL,
    full_name          VARCHAR(50) NOT NULL,
    national_id_number VARCHAR(20) NOT NULL,
    phone_number       VARCHAR(15) NOT NULL,
    gender             VARCHAR(10),
    occupation         VARCHAR(100),
    tracking_level     VARCHAR(15),
    organization_id    VARCHAR(50),
    CONSTRAINT fk_users_organization
        FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

CREATE TABLE IF NOT EXISTS user_roles (
    user_id VARCHAR(50) NOT NULL,
    role_id VARCHAR(50) NOT NULL,
    PRIMARY KEY (user_id, role_id),
    CONSTRAINT fk_user_roles_user
        FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_user_roles_role
        FOREIGN KEY (role_id) REFERENCES roles(id)
);

CREATE TABLE IF NOT EXISTS tokens (
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL,
    deleted_at TIMESTAMP(6),
    token      VARCHAR(255) NOT NULL UNIQUE,
    expired_at TIMESTAMP(6),
    revoked_at TIMESTAMP(6),
    user_id    VARCHAR(50) NOT NULL,
    CONSTRAINT fk_tokens_user
        FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Category and complaint system tables
CREATE TABLE IF NOT EXISTS categories (
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6) NOT NULL,
    updated_at  TIMESTAMP(6) NOT NULL,
    deleted_at  TIMESTAMP(6),
    name        VARCHAR(50) NOT NULL,
    description TEXT
);

CREATE TABLE IF NOT EXISTS complaints (
    id                    VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at            TIMESTAMP(6) NOT NULL,
    updated_at            TIMESTAMP(6) NOT NULL,
    deleted_at            TIMESTAMP(6),
    title                 VARCHAR(100) NOT NULL,
    description           TEXT,
    type                  VARCHAR(36) NOT NULL,
    status                VARCHAR(50) NOT NULL,
    location              JSON,
    contact               JSON,
    tracking_level        VARCHAR(36),
    identification_number VARCHAR(100),
    user_id               VARCHAR(50),
    CONSTRAINT fk_complaints_user
        FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS complaint_categories (
    complaint_id VARCHAR(50) NOT NULL,
    category_id  VARCHAR(50) NOT NULL,
    PRIMARY KEY (complaint_id, category_id),
    CONSTRAINT fk_complaint_categories_complaint
        FOREIGN KEY (complaint_id) REFERENCES complaints(id),
    CONSTRAINT fk_complaint_categories_category
        FOREIGN KEY (category_id) REFERENCES categories(id)
);

CREATE TABLE IF NOT EXISTS followups (
    id           VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at   TIMESTAMP(6) NOT NULL,
    updated_at   TIMESTAMP(6) NOT NULL,
    deleted_at   TIMESTAMP(6),
    comment      TEXT NOT NULL,
    action       VARCHAR(36) NOT NULL,
    position     VARCHAR(36),
    created_by   VARCHAR(50) NOT NULL,
    complaint_id VARCHAR(50) NOT NULL,
    CONSTRAINT fk_followups_complaint
        FOREIGN KEY (complaint_id) REFERENCES complaints(id)
);

CREATE TABLE IF NOT EXISTS documents (
    id           VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at   TIMESTAMP(6) NOT NULL,
    updated_at   TIMESTAMP(6) NOT NULL,
    deleted_at   TIMESTAMP(6),
    type         VARCHAR(36) NOT NULL,
    url          TEXT NOT NULL,
    complaint_id VARCHAR(50) NOT NULL,
    followup_id  VARCHAR(50),
    CONSTRAINT fk_documents_complaint
        FOREIGN KEY (complaint_id) REFERENCES complaints(id),
    CONSTRAINT fk_documents_followup
        FOREIGN KEY (followup_id) REFERENCES followups(id)
);

CREATE TABLE IF NOT EXISTS organization_categories (
    id              VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at      TIMESTAMP(6) NOT NULL,
    updated_at      TIMESTAMP(6) NOT NULL,
    deleted_at      TIMESTAMP(6),
    category_id     VARCHAR(50) NOT NULL,
    organization_id VARCHAR(50) NOT NULL,
    CONSTRAINT fk_organization_categories_category
        FOREIGN KEY (category_id) REFERENCES categories(id),
    CONSTRAINT fk_organization_categories_organization
        FOREIGN KEY (organization_id) REFERENCES organizations(id)
);