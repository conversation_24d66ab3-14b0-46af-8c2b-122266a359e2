CREATE TABLE IF NOT EXISTS permissions
(
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6),
    deleted_at  TIMESTAMP(6),
    updated_at  TIMESTAMP(6),
    description VARCHAR(255),
    name        VARCHAR(30) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS provinces
(
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    updated_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL
);

CREATE TABLE IF NOT EXISTS districts
(
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6),
    deleted_at  TIMESTAMP(6),
    updated_at  TIMESTAMP(6),
    code        VARCHAR(10),
    name        VARCHAR(10) NOT NULL,
    province_id VARCHAR(50) NOT NULL REFERENCES provinces
);

CREATE TABLE IF NOT EXISTS roles
(
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6),
    deleted_at  TIMESTAMP(6),
    updated_at  TIMESTAMP(6),
    description VARCHAR(50),
    name        VARCHAR(15) NOT NULL UNIQUE,
    valid_from  TIMESTAMP(6),
    valid_to    TIMESTAMP(6)
);

CREATE TABLE IF NOT EXISTS role_permissions
(
    role_id       VARCHAR(50) NOT NULL REFERENCES roles,
    permission_id VARCHAR(50) NOT NULL REFERENCES permissions
);

CREATE TABLE IF NOT EXISTS sectors
(
    id          VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at  TIMESTAMP(6),
    deleted_at  TIMESTAMP(6),
    updated_at  TIMESTAMP(6),
    code        VARCHAR(10),
    name        VARCHAR(20) NOT NULL,
    district_id VARCHAR(50) NOT NULL REFERENCES districts
);

CREATE TABLE IF NOT EXISTS cells
(
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    updated_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL,
    sector_id  VARCHAR(50) NOT NULL REFERENCES sectors
);

CREATE TABLE IF NOT EXISTS organizations
(
    id         VARCHAR(50)  NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    updated_at TIMESTAMP(6),
    is_public  BOOLEAN      NOT NULL DEFAULT TRUE,
    name       VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS users
(
    id                 VARCHAR(50)  NOT NULL PRIMARY KEY,
    created_at         TIMESTAMP(6),
    deleted_at         TIMESTAMP(6),
    updated_at         TIMESTAMP(6),
    email              VARCHAR(150) UNIQUE,
    gender             VARCHAR(10),
    full_name          VARCHAR(50)  NOT NULL,
    national_id_number VARCHAR(20)  NOT NULL,
    phone_number       VARCHAR(15)  NOT NULL,
    is_active          BOOLEAN,
    is_admin           BOOLEAN,
    occupation         VARCHAR(100),
    password           VARCHAR(150) NOT NULL,
    tracking_level     VARCHAR(15),
    username           VARCHAR(30),
    organization_id    VARCHAR(50) REFERENCES organizations
);

CREATE TABLE IF NOT EXISTS tokens
(
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    updated_at TIMESTAMP(6),
    expired    BOOLEAN     NOT NULL DEFAULT FALSE,
    revoked    BOOLEAN     NOT NULL DEFAULT FALSE,
    token      VARCHAR(255) UNIQUE,
    user_id    VARCHAR(50) REFERENCES users
);

CREATE TABLE IF NOT EXISTS user_roles
(
    user_id VARCHAR(50) NOT NULL REFERENCES users,
    role_id VARCHAR(50) NOT NULL REFERENCES roles
);

CREATE TABLE IF NOT EXISTS villages
(
    id         VARCHAR(50) NOT NULL PRIMARY KEY,
    created_at TIMESTAMP(6),
    deleted_at TIMESTAMP(6),
    updated_at TIMESTAMP(6),
    code       VARCHAR(10),
    name       VARCHAR(20) NOT NULL,
    cell_id    VARCHAR(50) NOT NULL REFERENCES cells
);

