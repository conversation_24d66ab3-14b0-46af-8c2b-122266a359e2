package minaloc.mbaza.api.complaint.service;

import minaloc.mbaza.api.complaint.dtos.ComplaintFilterDTO;
import minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

public interface ComplaintService {
    String createComplaint(CreateComplaintDTO.Input input);

    PagedModel<ViewComplaintDTO.Output> getAllComplaints(Pageable page, ComplaintFilterDTO.Input filters);

    ViewComplaintDTO.Output getComplaintById(String id);

}
