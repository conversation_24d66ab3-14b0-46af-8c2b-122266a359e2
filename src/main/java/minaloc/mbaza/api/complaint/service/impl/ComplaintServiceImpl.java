package minaloc.mbaza.api.complaint.service.impl;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.complaint.dtos.ComplaintFilterDTO;
import minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO;
import minaloc.mbaza.api.complaint.service.ComplaintService;
import minaloc.mbaza.api.complaint.usecases.CreateComplaintUseCase;
import minaloc.mbaza.api.complaint.usecases.GetAllComplaintsUseCase;
import minaloc.mbaza.api.complaint.usecases.GetComplaintByID;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class ComplaintServiceImpl implements ComplaintService {
    private final CreateComplaintUseCase createComplaintUseCase;
    private final GetAllComplaintsUseCase getAllComplaintsUseCase;
    private final GetComplaintByID getComplaintByID;

    @Override
    public String createComplaint(CreateComplaintDTO.Input input) {
        return createComplaintUseCase.execute(input);

    }

    @Override
    public PagedModel<ViewComplaintDTO.Output> getAllComplaints(Pageable page, ComplaintFilterDTO.Input filters) {
        return getAllComplaintsUseCase.execute(page, filters);
    }

    @Override
    public ViewComplaintDTO.Output getComplaintById(String id) {
        return getComplaintByID.execute(id);
    }

}
