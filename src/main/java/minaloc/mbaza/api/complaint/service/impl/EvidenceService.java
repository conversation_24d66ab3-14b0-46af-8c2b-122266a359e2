package minaloc.mbaza.api.complaint.service.impl;
import lombok.AllArgsConstructor;
import minaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO;
import minaloc.mbaza.api.complaint.exceptions.FileUploadException;
import minaloc.mbaza.api.complaint.exceptions.NoValidFilesException;
import minaloc.mbaza.api.complaint.service.FileStorageService;
import minaloc.mbaza.api.complaint.usecases.MinioService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;


@Service
@AllArgsConstructor
public class EvidenceService implements FileStorageService {
    private final MinioService minioService;

    @Override
    public List<ViewFileUploadedDTO.Output> uploadFiles(List<MultipartFile> files) {

        if (files == null || files.isEmpty()) {
            throw new FileUploadException("No files provided for upload.");
        }

        List<ViewFileUploadedDTO.Output> results = new ArrayList<>();

        for (MultipartFile file : files) {
            if (file.isEmpty()) continue;

            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.trim().isEmpty()) continue;

            try {
                byte[] bytes = file.getBytes();
                ViewFileUploadedDTO.Output result = minioService.uploadFile(bytes, fileName);
                results.add(result);
            } catch (Exception e) {
                throw new FileUploadException("Failed to upload file: " + fileName, e);
            }
        }

        if (results.isEmpty()) {
            throw new NoValidFilesException("No valid files were uploaded.");
        }

        return results;

    }

    @Override
    public String generateFileUrl(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty");
        }
        return minioService.generateFileUrl(fileName);
    }
}
