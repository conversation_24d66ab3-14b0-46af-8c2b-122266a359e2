package minaloc.mbaza.api.complaint.enums;

import lombok.Getter;

@Getter
public enum FollowUpAction {
    RESOLVE("Resolve"),
    ESCALATE("Escalate"),
    CLOSE("Close"),
    REOPEN("Reopen"),
    CREATED("Created"),
    DELETED("Deleted"),
    ASSIGNED("Assigned"),
    UNASSIGNED("Unassigned"),
    UPDATED("Updated");

    private final String action;

    FollowUpAction(String action) {
        this.action = action;
    }

}
