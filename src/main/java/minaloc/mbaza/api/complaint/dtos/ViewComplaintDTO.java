package minaloc.mbaza.api.complaint.dtos;

import lombok.Getter;
import lombok.Setter;
import minaloc.mbaza.api.complaint.domains.Contact;
import minaloc.mbaza.api.complaint.domains.Location;
import minaloc.mbaza.api.complaint.enums.ComplaintType;

import java.time.LocalDateTime;
import java.util.List;

public final class ViewComplaintDTO {

    private ViewComplaintDTO() {
        // Prevent instantiation
    }

    @Setter
    @Getter
    public static class Output {
        String id;
        String title;
        String description;
        ComplaintType type;
        Location location;
        String trackingLevel;
        String identificationNumber;
        Contact contact;
        String status;
        String userId;
        List<ViewCategoryDTO.Output> categories;
        List<ViewDocumentDTO.Output> documents;
        List<ViewFollowUpDTO.Output> followUps;
        LocalDateTime createdAt;
        LocalDateTime updatedAt;


    }

}
