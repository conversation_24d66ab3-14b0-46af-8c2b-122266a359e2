package minaloc.mbaza.api.complaint.dtos;

import lombok.Data;

import java.time.LocalDateTime;

public final class ViewDocumentDTO {
    private ViewDocumentDTO() {
        // Private constructor to prevent instantiation
    }


    @Data
    public static class Output {
        private String id;
        private String type;
        private String url;
        private String complaintId;
        private String followUpId;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
}
