package minaloc.mbaza.api.complaint.dtos;

import minaloc.mbaza.api.complaint.domains.Contact;
import minaloc.mbaza.api.complaint.domains.Location;
import minaloc.mbaza.api.complaint.enums.ComplaintStatus;
import minaloc.mbaza.api.complaint.enums.ComplaintType;
import minaloc.mbaza.api.complaint.enums.TrackingLevel;

import java.util.Set;

public final class UpdateComplaintDTO {

    private UpdateComplaintDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            String title,
            String description,
            ComplaintType type,
            Location location,
            TrackingLevel trackingLevel,
            String identificationNumber,
            Contact contact,
            ComplaintStatus status,
            Set<String> categoryIds
    ) {
    }
}
