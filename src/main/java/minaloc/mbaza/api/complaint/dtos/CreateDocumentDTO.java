package minaloc.mbaza.api.complaint.dtos;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

public final class CreateDocumentDTO {

    private CreateDocumentDTO() {
        // Prevent instantiation
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Input {

        @NotBlank(message = "validation.document.type.required")
        private String type;

        @NotBlank(message = "validation.document.url.required")
        private String url;
    }


}
