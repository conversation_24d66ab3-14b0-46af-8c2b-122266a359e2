package minaloc.mbaza.api.complaint.dtos;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import minaloc.mbaza.api.complaint.domains.Contact;
import minaloc.mbaza.api.complaint.domains.Location;

import java.util.List;

public final class CreateComplaintDTO {

    private CreateComplaintDTO() {
        // Prevent instantiation
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Input {
        @NotBlank(message = "validation.complaint.title.required")
        private String title;

        @NotBlank(message = "validation.complaint.description.required")
        private String description;

        @Valid
        private Location location;

        @Valid
        private Contact contact;

        @Valid
        private List<CreateDocumentDTO.Input> supportingDocuments;
    }

}
