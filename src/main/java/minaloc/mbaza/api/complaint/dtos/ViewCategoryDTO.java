package minaloc.mbaza.api.complaint.dtos;

import lombok.Builder;

import java.time.LocalDateTime;


public final class ViewCategoryDTO {
    private ViewCategoryDTO() {
        // Private constructor to prevent instantiation
    }

    @Builder
    public record Output(
            String name,
            String Description,
            LocalDateTime createdAt,
            LocalDateTime updatedAt
    ) {
    }
}
