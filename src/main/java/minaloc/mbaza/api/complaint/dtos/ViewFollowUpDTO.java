package minaloc.mbaza.api.complaint.dtos;

import lombok.*;

import java.time.LocalDateTime;

public final class ViewFollowUpDTO {

    private ViewFollowUpDTO() {
        // Private constructor to prevent instantiation
    }


    @Getter
    @Builder
    @AllArgsConstructor
    @Setter
    @NoArgsConstructor
    public static class Output {
        private String id;
        private String comment;
        private String createdBy;
        private String position;
        private String complaintId;
        private String action;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
}