package minaloc.mbaza.api.complaint.dtos;

import jakarta.validation.constraints.NotBlank;

public final class CategoryCreateDTO {
    private CategoryCreateDTO() {
    }

    public record Input(
            @NotBlank(message = "validation.category.name.required")
            String name,

            @NotBlank(message = "validation.category.description.required")
            String description
    ) {
    }
}
