package minaloc.mbaza.api.complaint.dtos;

// Use Spring's @Nullable annotation instead of Jakarta

import org.springframework.lang.Nullable;

public final class ComplaintFilterDTO {
    private ComplaintFilterDTO() {
        // Private constructor to prevent instantiation
    }

    public record Input(
            @Nullable
            String identificationNumber
            // Future filters can be added here
            // @Nullable String status,
            // @Nullable String category,
            // @Nullable LocalDate fromDate,
            // @Nullable LocalDate toDate
    ) {

    }
}