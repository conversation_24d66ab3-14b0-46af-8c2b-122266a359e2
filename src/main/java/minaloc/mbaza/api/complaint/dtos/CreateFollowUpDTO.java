package minaloc.mbaza.api.complaint.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import minaloc.mbaza.api.complaint.enums.FollowUpAction;

import java.util.List;

public final class CreateFollowUpDTO {

    private CreateFollowUpDTO() {
        // Private constructor to prevent instantiation
    }


    public record Input(
            @NotBlank(message = "Comment is required")
            String comment,

            @NotBlank(message = "Created by is required")
            String createdBy,

            @NotBlank(message = "Position is required")
            String position,

            String description,

            @NotNull(message = "Complaint ID is required")
            String complaintId,

            List<CreateDocumentDTO.Input> supportingDocuments,

            @NotNull(message = "Action is required")
            FollowUpAction action

    ) {

    }


}
