package minaloc.mbaza.api.complaint.domains;

import jakarta.persistence.*;
import lombok.*;
import minaloc.mbaza.api.common.domain.BasicEntity;
import minaloc.mbaza.api.complaint.enums.ComplaintStatus;
import minaloc.mbaza.api.complaint.enums.ComplaintType;
import minaloc.mbaza.api.complaint.enums.TrackingLevel;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.type.SqlTypes;

import java.util.HashSet;
import java.util.Set;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "complaints")
@SQLDelete(sql = "UPDATE complaints SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?")
@SQLRestriction("deleted_at IS NULL")
public class Complaint extends BasicEntity {
    @Column(name = "title")
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private ComplaintType type;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "location", columnDefinition = "json")
    private Location location;

    @Column(name = "tracking_level")
    @Enumerated(EnumType.STRING)
    private TrackingLevel trackingLevel;

    @Column(name = "identification_number")
    private String identificationNumber;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "contact", columnDefinition = "json")
    private Contact contact;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ComplaintStatus status;

    @Column(name = "user_id")
    private String userId;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "complaint_categories",
            joinColumns = @JoinColumn(name = "complaint_id"),
            inverseJoinColumns = @JoinColumn(name = "category_id")
    )
    @Builder.Default
    private Set<Category> categories = new HashSet<>();

    @OneToMany(mappedBy = "complaint", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Document> documents = new HashSet<>();

    @OneToMany(mappedBy = "complaint", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<FollowUp> followUps = new HashSet<>();


}
