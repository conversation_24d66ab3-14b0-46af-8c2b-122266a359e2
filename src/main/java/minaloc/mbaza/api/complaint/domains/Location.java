package minaloc.mbaza.api.complaint.domains;

import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Location {
    @NotBlank(message = "District is required")
    private String district;

    @NotBlank(message = "District code is required")
    private String districtCode;

    @NotBlank(message = "Sector is required")
    private String sector;

    @NotBlank(message = "Sector code is required")
    private String sectorCode;

    @NotBlank(message = "Cell is required")
    private String cell;

    @NotBlank(message = "Cell code is required")
    private String cellCode;

    @NotBlank(message = "Village is required")
    private String village;

    @NotBlank(message = "Village code is required")
    private String villageCode;
}
