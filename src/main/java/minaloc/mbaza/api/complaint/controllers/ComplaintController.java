package minaloc.mbaza.api.complaint.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.complaint.dtos.ComplaintFilterDTO;
import minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO;
import minaloc.mbaza.api.complaint.service.ComplaintService;
import minaloc.mbaza.api.complaint.service.FileStorageService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/v1/complaints")
@Tag(name = "Complaints", description = "Endpoints for submitting and managing complaints")
@RequiredArgsConstructor
@PublicEndpoint
public class ComplaintController {
    private final ComplaintService complaintService;
    private final FileStorageService fileStorageService;

    @Operation(summary = "Upload multiple files for complaints")
    @PostMapping(value = "/files/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<GenericResponse<List<ViewFileUploadedDTO.Output>>> uploadFiles(
            @RequestPart("files") List<MultipartFile> files) {
        return GenericResponse.ok("Files uploaded successfully", fileStorageService.uploadFiles(files));
    }


    @Operation(summary = "Create a new complaint")
    @PostMapping
    public ResponseEntity<GenericResponse<String>> createComplaint(
            @Valid @RequestBody CreateComplaintDTO.Input input
    ) {
        var identificationNumber = complaintService.createComplaint(input);
        return GenericResponse.ok("Complaint created successfully", identificationNumber);
    }


    @Operation(summary = "Get all complaints")
    @GetMapping
    public ResponseEntity<GenericResponse<PagedModel<ViewComplaintDTO.Output>>> getAllComplaints(
            @Valid @ParameterObject Pageable pageable,
            @RequestParam(required = false) @ModelAttribute ComplaintFilterDTO.Input filters
    ) {
        var complaints = complaintService.getAllComplaints(pageable, filters);
        return GenericResponse.ok("Complaints retrieved successfully", complaints);
    }


    @Operation(summary = "Get a complaint by its ID")
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<ViewComplaintDTO.Output>> getComplaintById(
            @PathVariable String id
    ) {
        var complaint = complaintService.getComplaintById(id);
        return GenericResponse.ok("Complaint retrieved successfully", complaint);
    }
}