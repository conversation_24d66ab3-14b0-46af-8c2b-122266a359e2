package minaloc.mbaza.api.complaint.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.complaint.dtos.ViewCategoryDTO;
import minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.ViewDocumentDTO;
import minaloc.mbaza.api.complaint.dtos.ViewFollowUpDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Categories.CATEGORIES;
import static minaloc.mbaza.jooq.generated.tables.ComplaintCategories.COMPLAINT_CATEGORIES;
import static minaloc.mbaza.jooq.generated.tables.Complaints.COMPLAINTS;
import static minaloc.mbaza.jooq.generated.tables.Documents.DOCUMENTS;
import static minaloc.mbaza.jooq.generated.tables.Followups.FOLLOWUPS;


@Service
@AllArgsConstructor
public class GetComplaintByID {
    private final DSLContext dsl;
    private final MinioService minioService;

    public ViewComplaintDTO.Output execute(String complaintId) {
        var complaint = dsl.selectFrom(COMPLAINTS)
                .where(COMPLAINTS.ID.eq(complaintId))
                .and(COMPLAINTS.DELETED_AT.isNull())
                .fetchOneInto(ViewComplaintDTO.Output.class);

        if (complaint == null) {
            throw new NotFoundException("Complaint with ID " + complaintId + " not found");
        }
        var categories = JooqExtensions.fetchInto(
                dsl.select(CATEGORIES.asterisk())
                        .from(CATEGORIES)
                        .join(COMPLAINT_CATEGORIES).on(CATEGORIES.ID.eq(COMPLAINT_CATEGORIES.CATEGORY_ID))
                        .where(COMPLAINT_CATEGORIES.COMPLAINT_ID.eq(complaintId)),
                ViewCategoryDTO.Output.class,
                Pageable.unpaged()
        );
        complaint.setCategories(categories.getContent());
        var documents = JooqExtensions.fetchInto(
                dsl.selectFrom(DOCUMENTS)
                        .where(DOCUMENTS.COMPLAINT_ID.eq(complaintId)),
                ViewDocumentDTO.Output.class,
                Pageable.unpaged()
        );
        complaint.setDocuments(documents.getContent());

        var followUps = JooqExtensions.fetchInto(
                dsl.selectFrom(FOLLOWUPS)
                        .where(FOLLOWUPS.COMPLAINT_ID.eq(complaintId))
                        .orderBy(FOLLOWUPS.CREATED_AT.desc()),
                ViewFollowUpDTO.Output.class,
                Pageable.unpaged()
        );
        complaint.setFollowUps(followUps.getContent());
        if (complaint.getDocuments() != null) {
            for (var document : complaint.getDocuments()) {
                if (document != null && document.getUrl() != null) {
                    String realUrl = minioService.generateFileUrl(document.getUrl());
                    document.setUrl(realUrl);
                }
            }
        }

        return complaint;
    }
}
