package minaloc.mbaza.api.complaint.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.exceptions.NotFoundException;
import minaloc.mbaza.api.common.utils.SecurityUtils;
import minaloc.mbaza.api.complaint.domains.Complaint;
import minaloc.mbaza.api.complaint.domains.FollowUp;
import minaloc.mbaza.api.complaint.dtos.CreateFollowUpDTO;
import minaloc.mbaza.api.complaint.repositories.ComplaintRepository;
import minaloc.mbaza.api.complaint.repositories.FollowUpRepository;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class CreateFollowUpUseCase {
    private final FollowUpRepository followUpRepository;
    private final ComplaintRepository complaintRepository;

    public Void execute(CreateFollowUpDTO.Input input) {
        var currentUser = SecurityUtils.getCurrentUser();
        var createdBy = currentUser != null ? currentUser.getFullName() : "ANONYMOUS USER";

        Complaint complaint = complaintRepository.findById(input.complaintId())
                .orElseThrow(() -> new NotFoundException("Complaint not found with id: " + input.complaintId()));
        FollowUp followUp = new FollowUp();
        followUp.setComplaint(complaint);
        followUp.setComment(input.comment());
        followUp.setAction(input.action());
        followUp.setPosition(input.position() != null ? input.position() : "Unknown Position");
        followUp.setCreatedBy(createdBy);
        followUpRepository.save(followUp);
        return null;
    }


}
