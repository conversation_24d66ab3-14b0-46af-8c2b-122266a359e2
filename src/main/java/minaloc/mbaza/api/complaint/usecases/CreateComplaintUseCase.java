package minaloc.mbaza.api.complaint.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.utils.SecurityUtils;
import minaloc.mbaza.api.complaint.domains.Complaint;
import minaloc.mbaza.api.complaint.domains.Document;
import minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO;
import minaloc.mbaza.api.complaint.dtos.CreateFollowUpDTO;
import minaloc.mbaza.api.complaint.enums.ComplaintStatus;
import minaloc.mbaza.api.complaint.enums.ComplaintType;
import minaloc.mbaza.api.complaint.enums.FollowUpAction;
import minaloc.mbaza.api.complaint.handlers.ComplaintCreatedEvent;
import minaloc.mbaza.api.complaint.repositories.ComplaintRepository;
import minaloc.mbaza.api.complaint.repositories.DocumentRepository;
import minaloc.mbaza.api.complaint.util.IdentificationNumberGenerator;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CreateComplaintUseCase {

    private final DocumentRepository documentRepository;
    private final ComplaintRepository complaintRepository;
    private final ApplicationEventPublisher eventPublisher;


    @Transactional
    public String execute(CreateComplaintDTO.Input request) {
        var currentUser = SecurityUtils.getCurrentUser();
        var createdBy = currentUser != null ? currentUser.getFullName() : "ANONYMOUS USER";
        var userId = currentUser != null ? currentUser.getId() : null;
        Complaint complaint = new Complaint();

        complaint.setTitle(request.getTitle());
        complaint.setDescription(request.getDescription());
        complaint.setLocation(request.getLocation());
        complaint.setContact(request.getContact());
        complaint.setTrackingLevel(null);
        complaint.setIdentificationNumber(IdentificationNumberGenerator.generateIdentificationNumber());


        if (userId != null) {
            complaint.setUserId(currentUser.getId());
            complaint.setType(ComplaintType.NAMED);
        } else {
            complaint.setType(ComplaintType.ANONYMOUS);
        }
        complaint.setStatus(ComplaintStatus.PENDING);
        complaintRepository.save(complaint);
        if (request.getSupportingDocuments() != null && !request.getSupportingDocuments().isEmpty()) {

            var documents = request.getSupportingDocuments().stream()
                    .map(docDto -> {
                        Document doc = new Document();
                        doc.setComplaint(complaint);
                        doc.setType(docDto.getType());
                        doc.setUrl(docDto.getUrl());
                        return doc;
                    }).collect(Collectors.toSet());

            documentRepository.saveAll(documents);

            complaint.setDocuments(documents);
            var savedComplaint = complaintRepository.save(complaint);
            CreateFollowUpDTO.Input followUpInput = new CreateFollowUpDTO.Input(
                    "",
                    createdBy,
                    "Citizen",
                    "Complaint Created",
                    savedComplaint.getId(),
                    null,
                    FollowUpAction.CREATED
            );

            eventPublisher.publishEvent(new ComplaintCreatedEvent(this, followUpInput));
        }
        return complaint.getIdentificationNumber();
    }
}
