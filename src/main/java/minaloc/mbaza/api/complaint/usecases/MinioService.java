package minaloc.mbaza.api.complaint.usecases;
import io.minio.*;
import io.minio.http.Method;
import minaloc.mbaza.api.complaint.clients.minio.MinioProperties;
import minaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

@Service
public class MinioService {

    private static final Logger log = LoggerFactory.getLogger(MinioService.class);
    private static final Map<String, String> CONTENT_TYPES = new HashMap<>();

    static {
        CONTENT_TYPES.put("pdf", "application/pdf");
        CONTENT_TYPES.put("doc", "application/msword");
        CONTENT_TYPES.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        CONTENT_TYPES.put("xls", "application/vnd.ms-excel");
        CONTENT_TYPES.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        CONTENT_TYPES.put("ppt", "application/vnd.ms-powerpoint");
        CONTENT_TYPES.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        CONTENT_TYPES.put("txt", "text/plain");
        CONTENT_TYPES.put("csv", "text/csv");

        CONTENT_TYPES.put("jpg", "image/jpeg");
        CONTENT_TYPES.put("jpeg", "image/jpeg");
        CONTENT_TYPES.put("png", "image/png");
        CONTENT_TYPES.put("gif", "image/gif");
        CONTENT_TYPES.put("bmp", "image/bmp");
        CONTENT_TYPES.put("svg", "image/svg+xml");
        CONTENT_TYPES.put("webp", "image/webp");
        CONTENT_TYPES.put("tiff", "image/tiff");
        CONTENT_TYPES.put("ico", "image/x-icon");

        CONTENT_TYPES.put("mp4", "video/mp4");
        CONTENT_TYPES.put("avi", "video/x-msvideo");
        CONTENT_TYPES.put("mov", "video/quicktime");
        CONTENT_TYPES.put("wmv", "video/x-ms-wmv");
        CONTENT_TYPES.put("flv", "video/x-flv");
        CONTENT_TYPES.put("webm", "video/webm");
        CONTENT_TYPES.put("mkv", "video/x-matroska");
        CONTENT_TYPES.put("3gp", "video/3gpp");

        CONTENT_TYPES.put("mp3", "audio/mpeg");
        CONTENT_TYPES.put("wav", "audio/wav");
        CONTENT_TYPES.put("ogg", "audio/ogg");
        CONTENT_TYPES.put("m4a", "audio/mp4");
        CONTENT_TYPES.put("flac", "audio/flac");
        CONTENT_TYPES.put("aac", "audio/aac");

        CONTENT_TYPES.put("zip", "application/zip");
        CONTENT_TYPES.put("rar", "application/vnd.rar");
        CONTENT_TYPES.put("7z", "application/x-7z-compressed");
        CONTENT_TYPES.put("tar", "application/x-tar");
        CONTENT_TYPES.put("gz", "application/gzip");
    }

    private final MinioClient minioClient;
    private final MinioProperties minioProperties;

    public MinioService(MinioClient minioClient, MinioProperties minioProperties) {
        this.minioClient = minioClient;
        this.minioProperties = minioProperties;
    }

    public ViewFileUploadedDTO.Output uploadFile(byte[] file, String originalFileName) {
        try {
            ensureBucketExists();
            String uniqueFileName = resolveUniqueFileName(originalFileName);
            String contentType = getContentType(uniqueFileName);
            uploadToMinio(file, uniqueFileName, contentType);
            return  ViewFileUploadedDTO.Output.builder()
                    .fileName(uniqueFileName)
                    .type(contentType)
                    .build();

        } catch (Exception e) {
            log.error("Error uploading file: {} - {}", originalFileName, e.getMessage());
            throw new RuntimeException("File upload failed: " + e.getMessage(), e);
        }
    }

    private void ensureBucketExists() throws Exception {
        String bucketName = minioProperties.getBucketName();
        boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder().bucket(bucketName).build()
        );

        if (!exists) {
            minioClient.makeBucket(
                    MakeBucketArgs.builder().bucket(bucketName).build()
            );
        }
    }

    private String resolveUniqueFileName(String fileName)  {
        String baseName = fileName;
        String extension = "";
        int i = fileName.lastIndexOf('.');
        if (i > 0) {
            extension = fileName.substring(i);
            baseName = fileName.substring(0, i);
        }

        String newFileName = fileName;
        int counter = 1;

        while (objectExists(newFileName)) {
            newFileName = baseName + "-" + counter + extension;
            counter++;
        }

        return newFileName;
    }

    private boolean objectExists(String fileName) {
        try {
            minioClient.statObject(StatObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void uploadToMinio(byte[] file, String fileName, String contentType) throws Exception {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(file)) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .stream(inputStream, file.length, -1)
                    .contentType(contentType)
                    .build();

            minioClient.putObject(args);
        }
    }

    public String generateFileUrl(String fileName) {
        try {
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .build();

            return minioClient.getPresignedObjectUrl(args);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate URL: " + e.getMessage(), e);
        }
    }

    public String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return CONTENT_TYPES.getOrDefault(extension, "application/octet-stream");
    }

}
