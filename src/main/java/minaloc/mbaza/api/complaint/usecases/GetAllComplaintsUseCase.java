package minaloc.mbaza.api.complaint.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.complaint.dtos.ComplaintFilterDTO;
import minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO;
import org.jooq.DSLContext;
import org.jooq.Select;
import org.jooq.SelectJoinStep;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Complaints.COMPLAINTS;

@Service
@AllArgsConstructor
public class GetAllComplaintsUseCase {
    private final DSLContext dsl;

    public PagedModel<ViewComplaintDTO.Output> execute(Pageable pageable, ComplaintFilterDTO.Input filters) {
        PagedModel<ViewComplaintDTO.Output> page;
        Select<?> query;
        if (filters != null && filters.identificationNumber() != null && !filters.identificationNumber().trim().isEmpty()) {
            query = dsl.selectFrom(COMPLAINTS)
                    .where(COMPLAINTS.IDENTIFICATION_NUMBER.eq(filters.identificationNumber()));
        } else {
            query = dsl.selectFrom(COMPLAINTS);
        }
        page = JooqExtensions.fetchInto((SelectJoinStep<?>) query, ViewComplaintDTO.Output.class, pageable);
        return page;
    }

}