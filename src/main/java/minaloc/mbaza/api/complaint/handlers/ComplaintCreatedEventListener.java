package minaloc.mbaza.api.complaint.handlers;
import lombok.AllArgsConstructor;
import minaloc.mbaza.api.complaint.dtos.CreateFollowUpDTO;
import minaloc.mbaza.api.complaint.usecases.CreateFollowUpUseCase;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class ComplaintCreatedEventListener {

    private final CreateFollowUpUseCase createFollowUpUseCase;

    @EventListener
    public void handleComplaintCreated(ComplaintCreatedEvent event) {
        CreateFollowUpDTO.Input input = event.getInput();

        createFollowUpUseCase.execute(input);
    }
}
