package minaloc.mbaza.api.common.config;

import jakarta.annotation.PostConstruct;
import minaloc.mbaza.jooq.generated.DefaultCatalog;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.impl.DefaultVisitListenerProvider;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.*;

@Configuration
public class JooqConfig {

    private final DefaultConfiguration configuration;

    public JooqConfig(DefaultConfiguration configuration) {
        this.configuration = configuration;
    }

    @PostConstruct
    public void init() {
        configuration.set(new DefaultVisitListenerProvider(new SoftDeleteFilter()));
    }
}

/**
 * VisitListener implementation for handling soft delete functionality.
 * This class intercepts SQL queries and modifies them to account for soft deleted rows.
 * This is to complement the row-level security setup at the database level.
 * The row-level security policies ensure that only non-deleted rows are visible in SELECT queries,
 * while allowing unrestricted INSERT, UPDATE, and DELETE operations.
 */
class SoftDeleteFilter implements VisitListener {

    private final ThreadLocal<Deque<List<Condition>>> conditionStacks = ThreadLocal.withInitial(ArrayDeque::new);
    private final ThreadLocal<Deque<Boolean>> whereStacks = ThreadLocal.withInitial(ArrayDeque::new);

    /**
     * Pushes a new context for conditions and where clauses.
     */
    private void push() {
        conditionStacks.get().push(new ArrayList<>());
        whereStacks.get().push(false);
    }

    /**
     * Pops the current context for conditions and where clauses.
     */
    private void pop() {
        if (!whereStacks.get().isEmpty()) {
            whereStacks.get().pop();
        }
        if (!conditionStacks.get().isEmpty()) {
            conditionStacks.get().pop();
        }
    }

    /**
     * Retrieves the current list of conditions.
     *
     * @return The list of conditions.
     */
    public List<Condition> conditions() {
        Deque<List<Condition>> stack = conditionStacks.get();
        return stack.isEmpty() ? new ArrayList<>() : stack.peek();
    }

    /**
     * Retrieves the current where clause state.
     *
     * @return The where clause state.
     */
    public boolean where() {
        Deque<Boolean> stack = whereStacks.get();
        return !stack.isEmpty() && stack.peek();
    }

    /**
     * Sets the where clause state.
     *
     * @param value The where clause state.
     */
    public void where(boolean value) {
        Deque<Boolean> stack = whereStacks.get();
        if (!stack.isEmpty()) {
            stack.pop();
        }
        stack.push(value);
    }

    /**
     * Adds soft delete condition for tables with deleted_at field.
     *
     * @param context The visit context.
     */
    private void addSoftDeleteConditions(VisitContext context) {
        QueryPart queryPart = context.queryPart();

        if (queryPart instanceof Table<?> table) {

            // Check if this table has a deleted_at field
            Field<LocalDateTime> deletedAtField = getDeletedAtField(table);
            if (deletedAtField != null) {
                List<Condition> currentConditions = conditions();
                Condition softDeleteCondition = deletedAtField.isNull();

                // Only add if not already present
                boolean conditionExists = currentConditions.stream()
                        .anyMatch(condition -> condition.toString().equals(softDeleteCondition.toString()));

                if (!conditionExists) {
                    currentConditions.add(softDeleteCondition);
                }
            }
        }
    }

    /**
     * Gets the deleted_at field from a table if it exists.
     *
     * @param table The table to check.
     * @return The deleted_at field or null if not found.
     */
    private Field<LocalDateTime> getDeletedAtField(Table<?> table) {
        try {
            return table.field("deleted_at", LocalDateTime.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Checks if the current context is in a SELECT query.
     *
     * @param context The visit context.
     * @return True if in a SELECT context.
     */
    private boolean isInSelectContext(VisitContext context) {
        QueryPart queryPart = context.queryPart();

        // Check if we're in a Select query
        QueryPart parent = context.queryPartsLength() > 1 ?
                context.queryParts()[context.queryPartsLength() - 2] : null;

        return queryPart instanceof Select || parent instanceof Select;
    }

    /**
     * Checks if we're currently processing a WHERE clause.
     *
     * @param context The visit context.
     * @return True if in WHERE clause context.
     */
    private boolean isInWhereContext(VisitContext context) {
        // Check the query parts stack to determine if we're in a WHERE context
        QueryPart[] parts = context.queryParts();
        for (int i = parts.length - 1; i >= 0; i--) {
            if (parts[i] instanceof Condition) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void visitStart(VisitContext context) {
        QueryPart queryPart = context.queryPart();

        // Push context for SELECT, UPDATE, DELETE queries
        if (queryPart instanceof Select ||
                queryPart instanceof Update ||
                queryPart instanceof Delete) {
            push();
        }

        // Add soft delete conditions for tables in SELECT context
        if (isInSelectContext(context)) {
            addSoftDeleteConditions(context);
        }
    }

    @Override
    public void visitEnd(VisitContext context) {
        QueryPart queryPart = context.queryPart();

        // Handle WHERE clause injection
        if (queryPart instanceof SelectWhereStep ||
                queryPart instanceof UpdateWhereStep ||
                queryPart instanceof DeleteWhereStep) {

            List<Condition> currentConditions = conditions();

            if (!currentConditions.isEmpty()) {
                // This is a simplified approach - in practice, you might need more sophisticated
                // logic to properly inject WHERE conditions into the query
                try {
                    if (queryPart instanceof SelectWhereStep) {
                        Condition combined = DSL.and(currentConditions);
                        // Note: Direct modification might not work - this is conceptual
                        // You might need to use query transformation instead
                    }
                } catch (Exception e) {
                    // Handle any exceptions during condition injection
                }
            }
        }

        // Check if we're in a WHERE context and set the flag
        if (isInWhereContext(context)) {
            where(true);
        }

        // Pop context for SELECT, UPDATE, DELETE queries
        if (queryPart instanceof Select ||
                queryPart instanceof Update ||
                queryPart instanceof Delete) {
            pop();
        }
    }

    /**
     * Alternative implementation using reflection for older JOOQ versions.
     * Use this if the visitor approach doesn't work with your JOOQ version.
     *
     * @param select The SELECT query.
     * @return Set of tables in the query.
     */
    @SuppressWarnings("unused")
    private Set<Table<?>> getTablesFromQueryReflection(Select<?> select) {
        Set<Table<?>> tables = new HashSet<>();

        try {
            // Get the query string and parse table names
            String sql = select.getSQL();

            // This is a basic regex approach - not foolproof but works for simple cases
            // You might need to enhance this based on your specific use cases
            String[] tokens = sql.toLowerCase().split("\\s+");

            boolean afterFrom = false;
            boolean afterJoin = false;

            for (String token : tokens) {
                if ("from".equals(token)) {
                    afterFrom = true;
                    continue;
                }

                if (token.contains("join")) {
                    afterJoin = true;
                    continue;
                }

                if ("where".equals(token) || "group".equals(token) ||
                        "order".equals(token) || "having".equals(token)) {
                    break;
                }

                if ((afterFrom || afterJoin) && !token.contains("(")) {
                    // Try to find the actual table from the generated catalog
                    Table<?> table = findTableByName(token);
                    if (table != null) {
                        tables.add(table);
                    }
                    afterFrom = false;
                    afterJoin = false;
                }
            }

        } catch (Exception e) {
            System.err.println("Error in reflection-based table extraction: " + e.getMessage());
        }

        return tables;
    }

    /**
     * Finds a table by name from the generated JOOQ catalog.
     *
     * @param tableName The name of the table to find.
     * @return The table if found, null otherwise.
     */
    private Table<?> findTableByName(String tableName) {
        try {
            // Clean up the table name (remove quotes, schema prefixes, etc.)
            String cleanTableName = tableName.replaceAll("[\"'`]", "").trim();

            // Remove schema prefix if present
            if (cleanTableName.contains(".")) {
                cleanTableName = cleanTableName.substring(cleanTableName.lastIndexOf(".") + 1);
            }

            // Search through all schemas in the catalog
            for (Schema schema : DefaultCatalog.DEFAULT_CATALOG.getSchemas()) {
                for (Table<?> table : schema.getTables()) {
                    if (table.getName().equalsIgnoreCase(cleanTableName)) {
                        return table;
                    }
                }
            }
        } catch (Exception e) {
            // Table not found or error occurred
        }

        return null;
    }
}
