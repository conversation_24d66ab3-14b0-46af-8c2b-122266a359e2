package minaloc.mbaza.api.common.config;

import jakarta.annotation.PostConstruct;
import minaloc.mbaza.api.common.filters.SoftDeleteFilter;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.impl.DefaultVisitListenerProvider;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JooqConfig {

    private final DefaultConfiguration configuration;

    public JooqConfig(DefaultConfiguration configuration) {
        this.configuration = configuration;
    }

    @PostConstruct
    public void init() {
        configuration.set(new DefaultVisitListenerProvider(new SoftDeleteFilter()));
    }
}

