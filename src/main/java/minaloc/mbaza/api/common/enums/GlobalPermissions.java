package minaloc.mbaza.api.common.enums;

import lombok.Getter;

@Getter
public enum GlobalPermissions {
    CREATE_ROLE("Can create role"),
    UPDATE_ROLE("Can update role"),
    DELETE_ROLE("Can delete role"),
    VIEW_ROLE("Can view role"),
    CREATE_ADMIN("Can create admin"),
    UPDATE_ADMIN("Can update admin"),
    DELETE_ADMIN("Can delete admin"),
    FULL_ACCESS("Can access everything"),
    VIEW_ADMIN("Can view admin"),
    CREATE_COMPLAINT("Can create complaint"),
    UPDATE_COMPLAINT("Can update complaint"),
    VIEW_COMPLAINT("Can view complaint");

    private final String description;

    GlobalPermissions(String description) {
        this.description = description;
    }

}

