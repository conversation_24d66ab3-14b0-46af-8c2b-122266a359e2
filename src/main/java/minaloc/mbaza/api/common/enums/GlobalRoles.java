package minaloc.mbaza.api.common.enums;

import lombok.Getter;

import java.util.List;

/**
 * Enum representing global roles in the system.
 * Each role has a description and a list of permissions.
 */
@Getter
public enum GlobalRoles {
    ADMIN("Admin",
            List.of(GlobalPermissions.CREATE_ADMIN, GlobalPermissions.VIEW_ADMIN, GlobalPermissions.UPDATE_ADMIN,
                    GlobalPermissions.DELETE_ADMIN, GlobalPermissions.CREATE_ROLE, GlobalPermissions.VIEW_ROLE,
                    GlobalPermissions.UPDATE_ROLE, GlobalPermissions.DELETE_ROLE)),
    SUPER_ADMIN("Super Admin", List.of(GlobalPermissions.FULL_ACCESS)),
    CITIZEN("Citizen", List.of(GlobalPermissions.CREATE_COMPLAINT,
            GlobalPermissions.UPDATE_COMPLAINT, GlobalPermissions.VIEW_COMPLAINT));

    private final String description;
    private final List<GlobalPermissions> permissions;

    GlobalRoles(String description, List<GlobalPermissions> permissions) {
        this.description = description;
        this.permissions = permissions;
    }
}