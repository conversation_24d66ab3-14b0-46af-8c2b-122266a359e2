package minaloc.mbaza.api.common.enums;

public final class GlobalPermissionsConstants {

    // Role permissions
    public static final String CREATE_ROLE = "CREATE_ROLE";
    public static final String UPDATE_ROLE = "UPDATE_ROLE";
    public static final String DELETE_ROLE = "DELETE_ROLE";
    public static final String VIEW_ROLE = "VIEW_ROLE";

    // Admin permissions
    public static final String CREATE_ADMIN = "CREATE_ADMIN";
    public static final String UPDATE_ADMIN = "UPDATE_ADMIN";
    public static final String DELETE_ADMIN = "DELETE_ADMIN";
    public static final String VIEW_ADMIN = "VIEW_ADMIN";
    public static final String FULL_ACCESS = "FULL_ACCESS";

    // Complaint permissions
    public static final String CREATE_COMPLAINT = "CREATE_COMPLAINT";
    public static final String UPDATE_COMPLAINT = "UPDATE_COMPLAINT";
    public static final String VIEW_COMPLAINT = "VIEW_COMPLAINT";


    private GlobalPermissionsConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
