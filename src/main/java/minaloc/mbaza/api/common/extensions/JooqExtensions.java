package minaloc.mbaza.api.common.extensions;

import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PagedModel;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class JooqExtensions {

    public static SelectLimitStep<? extends Record> ordered(SelectConditionStep<?> selectStep,
                                                            Pageable pageable,
                                                            TableLike<?> tableLike) {
        if (pageable.getSort().isEmpty()) {
            Field<?> idField = tableLike.field("id");
            if (idField != null) {
                return selectStep.orderBy(idField.asc());
            }
            return selectStep;
        }

        List<SortField<?>> orders = new ArrayList<>();
        for (Sort.Order order : pageable.getSort()) {
            Field<?> field = tableLike.field(order.getProperty());
            if (field != null) {
                if (order.getDirection() == Sort.Direction.ASC) {
                    orders.add(field.asc());
                } else {
                    orders.add(field.desc());
                }
            }
        }

        try {
            return selectStep.orderBy(orders.toArray(new SortField[0]));
        } catch (Exception e) {
            String orderInfo = orders.stream()
                    .map(o -> {
                        o.getOrder();
                        return o.getName() + "-" + o.getOrder().name();
                    })
                    .collect(Collectors.joining(", "));
            System.out.println("Invalid sort fields or direction: [" + orderInfo + "] [" + e.getMessage() + "]");

            Field<?> idField = tableLike.field("id");
            if (idField != null) {
                return selectStep.orderBy(idField.desc());
            }
            return selectStep;
        }
    }

    private static Integer calculateTotalCount(Select<?> select) {
        Configuration configuration = select.configuration();
        SelectJoinStep<Record1<Integer>> countQuery = DSL.using(configuration)
                .selectCount()
                .from(select.asTable("count_base"));

        Record1<Integer> result = countQuery.fetchOne();
        return result != null ? result.value1() : 0;
    }

    public static <T> PagedModel<T> fetchInto(SelectJoinStep<?> selectStep,
                                              Class<T> type,
                                              Pageable pageable) {
        Integer totalCount = calculateTotalCount(selectStep);

        Select<?> query = selectStep;
        if (pageable.isPaged()) {
            query = selectStep.offset((int) pageable.getOffset()).limit(pageable.getPageSize());
        }

        List<T> result = query.fetchInto(type);
        return new PagedModel<>(new PageImpl<>(result, pageable, totalCount));
    }

    public static <T> PagedModel<T> fetchInto(SelectSeekStep1<?, ?> selectStep,
                                              Class<T> type,
                                              Pageable pageable) {
        Integer totalCount = calculateTotalCount(selectStep);

        Select<?> query = selectStep;
        if (pageable.isPaged()) {
            query = selectStep.offset((int) pageable.getOffset()).limit(pageable.getPageSize());
        }

        List<T> result = query.fetchInto(type);
        return new PagedModel<>(new PageImpl<>(result, pageable, totalCount));
    }

    public static <T> PagedModel<T> fetchInto(SelectConditionStep<?> selectStep,
                                              Class<T> type,
                                              Pageable pageable) {
        Integer totalCount = calculateTotalCount(selectStep);

        Select<?> query = selectStep;
        if (pageable.isPaged()) {
            query = selectStep.offset((int) pageable.getOffset()).limit(pageable.getPageSize());
        }

        List<T> result = query.fetchInto(type);
        return new PagedModel<>(new PageImpl<>(result, pageable, totalCount));
    }
}