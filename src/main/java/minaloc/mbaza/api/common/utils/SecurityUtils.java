package minaloc.mbaza.api.common.utils;

import minaloc.mbaza.api.common.domain.IamUserDetails;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityUtils {

    private SecurityUtils() {
        // Private constructor to prevent instantiation
    }

    public static IamUserDetails getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication.getPrincipal() instanceof IamUserDetails iamUserDetails) {
            return iamUserDetails;
        }
        return null;
    }
}