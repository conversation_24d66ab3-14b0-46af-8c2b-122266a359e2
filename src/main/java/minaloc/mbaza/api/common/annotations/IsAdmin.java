package minaloc.mbaza.api.common.annotations;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static minaloc.mbaza.api.common.enums.GlobalRolesConstants.ADMIN;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@SecurityRequirements(value = {@SecurityRequirement(name = "bearerAuth")})
@HasExplicitRoles({ADMIN})
public @interface IsAdmin {
}
