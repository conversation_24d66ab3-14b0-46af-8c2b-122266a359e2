package minaloc.mbaza.api.common.annotations;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import org.springframework.security.access.prepost.PreAuthorize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static minaloc.mbaza.api.common.enums.GlobalPermissionsConstants.FULL_ACCESS;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@SecurityRequirements(value = {@SecurityRequirement(name = "bearerAuth")})
@PreAuthorize("hasAuthority('" + FULL_ACCESS + "') or hasAnyRole({value})")
public @interface HasFullAccessOrRole {
    String[] value();
}
