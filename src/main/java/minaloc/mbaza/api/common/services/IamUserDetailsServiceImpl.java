package minaloc.mbaza.api.common.services;

import static minaloc.mbaza.jooq.generated.tables.Permissions.PERMISSIONS;
import static minaloc.mbaza.jooq.generated.tables.RolePermissions.ROLE_PERMISSIONS;
import static minaloc.mbaza.jooq.generated.tables.Roles.ROLES;
import static minaloc.mbaza.jooq.generated.tables.UserRoles.USER_ROLES;
import static minaloc.mbaza.jooq.generated.tables.Users.USERS;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.domain.IamUserDetails;
import minaloc.mbaza.api.common.interfaces.IamUserDetailsService;

@Service
@RequiredArgsConstructor
public class IamUserDetailsServiceImpl implements IamUserDetailsService {

    private final DSLContext dsl;

    @Override
    public IamUserDetails findUserByUsername(String username) {
        var condition = USERS.USERNAME.eq(username).or(USERS.EMAIL.eq(username));

        return dsl.select(
                        USERS.ID,
                        USERS.USERNAME,
                        USERS.FULL_NAME,
                        USERS.NATIONAL_ID_NUMBER,
                        USERS.EMAIL,
                        USERS.PASSWORD,
                        USERS.VERIFIED_AT,
                        DSL.arrayAgg(ROLES.NAME).as("roles"),
                        DSL.arrayAgg(PERMISSIONS.NAME).as("permissions")
                )
                .from(USERS)
                .leftJoin(USER_ROLES).on(USERS.ID.eq(USER_ROLES.USER_ID))
                .leftJoin(ROLES).on(USER_ROLES.ROLE_ID.eq(ROLES.ID))
                .leftJoin(ROLE_PERMISSIONS).on(ROLE_PERMISSIONS.ROLE_ID.eq(ROLES.ID))
                .leftJoin(PERMISSIONS).on(ROLE_PERMISSIONS.PERMISSION_ID.eq(PERMISSIONS.ID))
                .where(condition)
                .groupBy(USERS.ID, USERS.USERNAME, USERS.EMAIL, USERS.PASSWORD, USERS.VERIFIED_AT)
                .fetchOne(record -> {
                    String[] rolesArray = record.get("roles", String[].class);
                    Set<String> roles = rolesArray != null
                            ? Arrays.stream(rolesArray)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet())
                            : Set.of();

                    String[] permissionsArray = record.get("permissions", String[].class);
                    Set<String> permissions = permissionsArray != null
                            ? Arrays.stream(permissionsArray)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet())
                            : Set.of();

                    return IamUserDetails.builder()
                            .id(record.get(USERS.ID))
                            .username(record.get(USERS.USERNAME))
                            .email(record.get(USERS.EMAIL))
                            .password(record.get(USERS.PASSWORD))
                           .verifiedAt(record.get(USERS.VERIFIED_AT))
                            .roles(roles)
                            .permissions(permissions)
                            .build();
                });
    }
}