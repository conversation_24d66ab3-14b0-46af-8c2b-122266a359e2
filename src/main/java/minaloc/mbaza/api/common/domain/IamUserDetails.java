package minaloc.mbaza.api.common.domain;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class IamUserDetails implements UserDetails {
    private String id;
    private String fullName;
    private String nationalIdNumber;
    private String username;
    private String email;
    private String password;
    private boolean isActive;
    private Set<String> roles;
    private Set<String> permissions;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roles != null
                ? roles.stream()
                .map(SimpleGrantedAuthority::new)
                .toList()
                : List.of(new SimpleGrantedAuthority("ROLE_USER"));
    }
}
