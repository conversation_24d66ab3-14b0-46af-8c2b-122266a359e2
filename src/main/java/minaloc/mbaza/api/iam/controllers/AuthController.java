package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO;
import minaloc.mbaza.api.iam.services.IamService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Authentication", description = "Authentication endpoints")
@PublicEndpoint
@RequiredArgsConstructor
public class AuthController {

    private final IamService service;

    @Operation(summary = "Citizen registration")
    @PostMapping("/register")
    public ResponseEntity<GenericResponse<Void>> registerUser(
            @Valid @RequestBody RegisterCitizenDTO.Input registerRequestDTO
    ) {
        service.registerCitizen(registerRequestDTO);
        return GenericResponse.ok("User registered successfully");
    }

    @Operation(summary = "Login a user")
    @PostMapping("/login")
    public ResponseEntity<GenericResponse<LoginDTO.Output>> authenticateUser(@Valid @RequestBody LoginDTO.Input loginRequestDTO) {
        LoginDTO.Output loginResponse = service.login(loginRequestDTO);
        return GenericResponse.ok("success.authenticated", loginResponse);
    }
}
