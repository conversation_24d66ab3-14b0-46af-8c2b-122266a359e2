package minaloc.mbaza.api.iam.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.IsAuthenticated;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.iam.dtos.UpdateUserProfileDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO;
import minaloc.mbaza.api.iam.services.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user")
@Tag(name = "Current User", description = "Current user profile endpoints")
@IsAuthenticated
@RequiredArgsConstructor
public class UsersController {

    private final UserService userService;

    @GetMapping("/me")
    @Operation(summary = "Get current user profile")
    public ResponseEntity<GenericResponse<ViewUserProfileDTO.Output>> getCurrentUserProfile() {
        ViewUserProfileDTO.Output user = userService.getUserProfile();
        return GenericResponse.ok("success.user.profile.fetched", user);
    }

    @PatchMapping("/me")
    @Operation(summary = "Update current user profile")
    public ResponseEntity<GenericResponse<Void>> updateCurrentUser(@Valid @RequestBody UpdateUserProfileDTO.Input dto) {
        userService.updateUserProfile(dto);
        return GenericResponse.ok("success.user.profile.updated");
    }
}
