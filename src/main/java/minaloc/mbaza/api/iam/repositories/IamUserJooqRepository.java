package minaloc.mbaza.api.iam.repositories;

import static minaloc.mbaza.jooq.generated.tables.Users.USERS;

import java.time.LocalDateTime;
import java.util.Optional;

import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.enums.EUserGender;
import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;

/**
 * JOOQ-based repository for User operations.
 * Provides optimized database queries using JOOQ instead of JPA.
 */
@Repository
@RequiredArgsConstructor
public class IamUserJooqRepository {

    private final DSLContext dsl;

    /**
     * Find user by email address.
     * 
     * @param email the email to search for
     * @return Optional containing the user if found
     */
    public Optional<User> findByEmail(String email) {
        Record record = dsl.selectFrom(USERS)
                .where(USERS.EMAIL.eq(email)
                        .and(USERS.DELETED_AT.isNull()))
                .fetchOne();

        return record != null ? Optional.of(mapRecordToUser(record)) : Optional.empty();
    }

    /**
     * Find user by ID.
     * 
     * @param id the user ID
     * @return Optional containing the user if found
     */
    public Optional<User> findById(String id) {
        Record record = dsl.selectFrom(USERS)
                .where(USERS.ID.eq(id)
                        .and(USERS.DELETED_AT.isNull()))
                .fetchOne();

        return record != null ? Optional.of(mapRecordToUser(record)) : Optional.empty();
    }

    /**
     * Check if email exists for a different user (excluding the given ID).
     * 
     * @param email the email to check
     * @param id the user ID to exclude
     * @return true if email exists for another user
     */
    public boolean existsByEmailAndIdNot(String email, String id) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.EMAIL.eq(email)
                                .and(USERS.ID.ne(id))
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if phone number exists for a different user (excluding the given ID).
     * 
     * @param phoneNumber the phone number to check
     * @param id the user ID to exclude
     * @return true if phone number exists for another user
     */
    public boolean existsByPhoneNumberAndIdNot(String phoneNumber, String id) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.PHONE_NUMBER.eq(phoneNumber)
                                .and(USERS.ID.ne(id))
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if email exists.
     * 
     * @param email the email to check
     * @return true if email exists
     */
    public boolean existsByEmail(String email) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.EMAIL.eq(email)
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if phone number exists.
     * 
     * @param phoneNumber the phone number to check
     * @return true if phone number exists
     */
    public boolean existsByPhoneNumber(String phoneNumber) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.PHONE_NUMBER.eq(phoneNumber)
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Save a new user or update an existing one.
     * 
     * @param user the user to save
     * @return the saved user
     */
    @Transactional
    public User save(User user) {
        if (user.getId() == null) {
            // Insert new user
            return insertUser(user);
        } else {
            // Update existing user
            return updateUser(user);
        }
    }

    /**
     * Insert a new user.
     */
    private User insertUser(User user) {
        Record record = dsl.insertInto(USERS)
                .set(USERS.FULL_NAME, user.getFullName())
                .set(USERS.NATIONAL_ID_NUMBER, user.getNationalIdNumber())
                .set(USERS.PHONE_NUMBER, user.getPhoneNumber())
                .set(USERS.USERNAME, user.getUsername())
                .set(USERS.EMAIL, user.getEmail())
                .set(USERS.PASSWORD, user.getPassword())
                .set(USERS.GENDER, user.getGender() != null ? user.getGender().name() : null)
                .set(USERS.OCCUPATION, user.getOccupation())
                .set(USERS.TRACKING_LEVEL, user.getTrackingLevel() != null ? user.getTrackingLevel().name() : null)
                .set(USERS.VERIFIED_AT, user.getVerifiedAt())
                .set(USERS.ORGANIZATION_ID, user.getOrganization() != null ? user.getOrganization().getId() : null)
                .returningResult()
                .fetchOne();

        return mapRecordToUser(record);
    }

    /**
     * Update an existing user.
     */
    private User updateUser(User user) {
        Record record = dsl.update(USERS)
                .set(USERS.FULL_NAME, user.getFullName())
                .set(USERS.NATIONAL_ID_NUMBER, user.getNationalIdNumber())
                .set(USERS.PHONE_NUMBER, user.getPhoneNumber())
                .set(USERS.USERNAME, user.getUsername())
                .set(USERS.EMAIL, user.getEmail())
                .set(USERS.PASSWORD, user.getPassword())
                .set(USERS.GENDER, user.getGender() != null ? user.getGender().name() : null)
                .set(USERS.OCCUPATION, user.getOccupation())
                .set(USERS.TRACKING_LEVEL, user.getTrackingLevel() != null ? user.getTrackingLevel().name() : null)
                .set(USERS.VERIFIED_AT, user.getVerifiedAt())
                .set(USERS.IS_ACTIVE, user.getIsActive())
                .set(USERS.IS_ADMIN, user.getIsAdmin())
                .set(USERS.ORGANIZATION_ID, user.getOrganization() != null ? user.getOrganization().getId() : null)
                .where(USERS.ID.eq(user.getId()))
                .returningResult()
                .fetchOne();

        return mapRecordToUser(record);
    }

    /**
     * Map a JOOQ record to a User entity.
     */
    private User mapRecordToUser(Record record) {
        User user = new User();
        user.setId(record.get(USERS.ID));
        user.setCreatedAt(record.get(USERS.CREATED_AT));
        user.setUpdatedAt(record.get(USERS.UPDATED_AT));
        user.setDeletedAt(record.get(USERS.DELETED_AT));
        user.setFullName(record.get(USERS.FULL_NAME));
        user.setNationalIdNumber(record.get(USERS.NATIONAL_ID_NUMBER));
        user.setPhoneNumber(record.get(USERS.PHONE_NUMBER));
        user.setUsername(record.get(USERS.USERNAME));
        user.setEmail(record.get(USERS.EMAIL));
        user.setPassword(record.get(USERS.PASSWORD));
        
        String genderStr = record.get(USERS.GENDER);
        if (genderStr != null) {
            user.setGender(EUserGender.valueOf(genderStr));
        }
        
        user.setOccupation(record.get(USERS.OCCUPATION));
        
        String trackingLevelStr = record.get(USERS.TRACKING_LEVEL);
        if (trackingLevelStr != null) {
            user.setTrackingLevel(EUserTrackingLevel.valueOf(trackingLevelStr));
        }
        
        user.setVerifiedAt(record.get(USERS.VERIFIED_AT));
        user.setIsActive(record.get(USERS.IS_ACTIVE));
        user.setIsAdmin(record.get(USERS.IS_ADMIN));
        
        // Note: Organization and Roles relationships would need separate queries
        // For now, we'll leave them null as they're not used in the current use cases
        
        return user;
    }

    /**
     * Delete user by ID (soft delete).
     * 
     * @param id the user ID
     */
    @Transactional
    public void deleteById(String id) {
        dsl.update(USERS)
                .set(USERS.DELETED_AT, LocalDateTime.now())
                .where(USERS.ID.eq(id))
                .execute();
    }

    /**
     * Check if user exists by ID.
     * 
     * @param id the user ID
     * @return true if user exists
     */
    public boolean existsById(String id) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.ID.eq(id)
                                .and(USERS.DELETED_AT.isNull()))
        );
    }
}
