package minaloc.mbaza.api.iam.repositories;

import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import static minaloc.mbaza.jooq.generated.tables.Users.USERS;

/**
 * JOOQ-based repository for User validation operations.
 * Provides optimized validation queries using JOOQ instead of JPA.
 */
@Repository
@RequiredArgsConstructor
public class IamUserValidationJooqRepository {

    private final DSLContext dsl;

    /**
     * Check if email exists for a different user (excluding the given ID).
     * 
     * @param email the email to check
     * @param id the user ID to exclude
     * @return true if email exists for another user
     */
    public boolean existsByEmailAndIdNot(String email, String id) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.EMAIL.eq(email)
                                .and(USERS.ID.ne(id))
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if phone number exists for a different user (excluding the given ID).
     * 
     * @param phoneNumber the phone number to check
     * @param id the user ID to exclude
     * @return true if phone number exists for another user
     */
    public boolean existsByPhoneNumberAndIdNot(String phoneNumber, String id) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.PHONE_NUMBER.eq(phoneNumber)
                                .and(USERS.ID.ne(id))
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if email exists.
     * 
     * @param email the email to check
     * @return true if email exists
     */
    public boolean existsByEmail(String email) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.EMAIL.eq(email)
                                .and(USERS.DELETED_AT.isNull()))
        );
    }

    /**
     * Check if phone number exists.
     * 
     * @param phoneNumber the phone number to check
     * @return true if phone number exists
     */
    public boolean existsByPhoneNumber(String phoneNumber) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USERS)
                        .where(USERS.PHONE_NUMBER.eq(phoneNumber)
                                .and(USERS.DELETED_AT.isNull()))
        );
    }
}
