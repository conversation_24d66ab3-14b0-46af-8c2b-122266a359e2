package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

public final class LoginDTO {
    public record Input(
            @Email(message = "Email is invalid")
            String email,

            @NotBlank(message = "Password is mandatory")
            String password
    ) {
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Output {
        String accessToken;
    }
}
