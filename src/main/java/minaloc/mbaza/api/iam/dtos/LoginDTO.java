package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public final class LoginDTO {
    private LoginDTO() {
    }

    public record Input(
            @Email(message = "Email is invalid")
            String email,

            @NotBlank(message = "Password is mandatory")
            String password
    ) {
    }

    public record Output(
            String accessToken
    ) {
    }
}
