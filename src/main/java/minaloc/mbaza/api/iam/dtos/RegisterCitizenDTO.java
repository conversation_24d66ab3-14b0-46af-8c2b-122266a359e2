package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import minaloc.mbaza.api.iam.enums.EUserGender;

public final class RegisterCitizenDTO {

    public record Input(

            @NotEmpty(message = "Names are required")
            String fullName,

            @NotEmpty(message = "National id number is required")
            String nationalIdNumber,

            @NotEmpty(message = "Phone number required")
            String phoneNumber,

            @Email(message = "Email is invalid")
            String email,

            EUserGender gender,

            @NotBlank(message = "Password is mandatory")
            @Size(min = 4, message = "Password length should be longer")
            String password
    ) {
    }
}
