package minaloc.mbaza.api.iam.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

public final class ViewUserProfileDTO {
    private ViewUserProfileDTO() {
        // Private constructor to prevent instantiation
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Output {
        String email;
        String username;
        String fullName;
        String gender;
        String phoneNumber;
        String nationalIdNumber;
    }
}
