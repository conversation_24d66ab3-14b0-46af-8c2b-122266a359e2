package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import minaloc.mbaza.api.iam.enums.EUserGender;

public final class UpdateUserProfileDTO {
    private UpdateUserProfileDTO() {
        // Private constructor to prevent instantiation
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Input {
        @Email(message = "Email should be valid")
        String email;
        String username;
        String fullName;
        EUserGender gender;
        String phoneNumber;
        String nationalIdNumber;
    }
}
