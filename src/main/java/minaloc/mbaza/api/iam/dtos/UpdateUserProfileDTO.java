package minaloc.mbaza.api.iam.dtos;

import jakarta.validation.constraints.Email;
import minaloc.mbaza.api.iam.enums.EUserGender;

public final class UpdateUserProfileDTO {
    private UpdateUserProfileDTO() {
    }

    public record Input(
            @Email(message = "Email should be valid")
            String email,
            String username,
            String fullName,
            EUserGender gender,
            String phoneNumber,
            String nationalIdNumber
    ) {
    }
}
