package minaloc.mbaza.api.iam.seeds;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.enums.GlobalPermissions;
import minaloc.mbaza.api.common.enums.GlobalRoles;
import minaloc.mbaza.api.iam.domain.Permission;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.enums.EUserGender;
import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;
import minaloc.mbaza.api.iam.repositories.PermissionRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Order(101)
@RequiredArgsConstructor
public class UserRolesSeed implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(UserRolesSeed.class);

    private final Environment environment;
    private final PasswordEncoder passwordEncoder;
    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;

    @Override
    public void run(String... args) {
        Boolean refreshRoles = environment.getProperty("application.users.refresh-roles", Boolean.class);
        if (refreshRoles != null && !refreshRoles) {
            logger.info("Skipping roles and permissions initialization");
            return;
        }

        initPermissions();
        initializeRoles();
        assignPermissionsToRoles();
        createAdmin();
    }

    @Transactional
    protected void initPermissions() {
        logger.info("Initializing permissions");

        List<Permission> existingPermissions = permissionRepository.findAll();
        List<String> existingPermissionNames = existingPermissions.stream()
                .map(Permission::getName)
                .toList();

        GlobalPermissions[] globalPermissions = GlobalPermissions.values();
        List<GlobalPermissions> newPermissions = Arrays.stream(globalPermissions)
                .filter(perm -> !existingPermissionNames.contains(perm.name()))
                .toList();

        if (!newPermissions.isEmpty()) {
            List<Permission> permissionsToSave = newPermissions.stream()
                    .map(perm -> Permission.builder()
                            .name(perm.name())
                            .description(perm.getDescription())
                            .build())
                    .toList();

            permissionRepository.saveAll(permissionsToSave);
            logger.info("Inserted [{}] new permissions", permissionsToSave.size());
        }

        logger.info("Permissions initialized!!");
    }

    @Transactional
    protected void initializeRoles() {
        logger.info("Initializing roles");

        List<Role> existingRoles = roleRepository.findAll();
        List<String> existingRoleNames = existingRoles.stream()
                .map(Role::getName)
                .toList();

        GlobalRoles[] globalRoles = GlobalRoles.values();
        List<GlobalRoles> newRoles = Arrays.stream(globalRoles)
                .filter(role -> !existingRoleNames.contains(role.name()))
                .toList();

        if (!newRoles.isEmpty()) {
            List<Role> rolesToSave = newRoles.stream()
                    .map(role -> Role.builder()
                            .name(role.name())
                            .description(role.getDescription())
                            .build())
                    .toList();

            roleRepository.saveAll(rolesToSave);
            logger.info("Inserted [{}] new roles", rolesToSave.size());
        }

        logger.info("Roles initialized!!");
    }

    @Transactional
    protected void assignPermissionsToRoles() {
        logger.info("Assigning permissions to roles");

        GlobalRoles[] globalRoles = GlobalRoles.values();
        for (GlobalRoles globalRole : globalRoles) {
            Optional<Role> roleOpt = roleRepository.findByNameWithPermissions(globalRole.name());
            if (roleOpt.isEmpty()) {
                logger.error("Role [{}] not found when assigning permissions", globalRole.name());
                continue;
            }

            Role role = roleOpt.get();
            List<Permission> rolePermissions = globalRole.getPermissions().stream()
                    .map(perm -> permissionRepository.findByName(perm.name())
                            .orElseThrow(() -> new RuntimeException("Permission not found: " + perm.name())))
                    .toList();

            // Always update permissions to ensure consistency
            role.setPermissions(rolePermissions.stream().collect(Collectors.toSet()));
            roleRepository.save(role);
            logger.info("Updated permissions for role [{}] with [{}] permissions",
                    globalRole.name(), rolePermissions.size());
        }

        logger.info("Permission assignment completed!!");
    }

    @Transactional
    protected void createAdmin() {
        logger.info("Checking for backdoor Admin credentials");

        String adminEmail = environment.getProperty("application.users.admin.email");
        String adminPassword = environment.getProperty("application.users.admin.password");

        if ((adminEmail == null || adminEmail.isBlank()) &&
                (adminPassword == null || adminPassword.isBlank())) {
            logger.info("Backdoor Admin credentials not provided");
            return;
        }

        if (userRepository.findByEmail(adminEmail).isPresent()) {
            logger.info("Backdoor Admin already exists");
            return;
        }

        logger.info("Creating backdoor admin");

        Optional<Role> adminRoleOpt = roleRepository.findByNameWithPermissions(GlobalRoles.ADMIN.name());
        Optional<Role> superAdminRoleOpt = roleRepository.findByNameWithPermissions(GlobalRoles.SUPER_ADMIN.name());

        if (adminRoleOpt.isEmpty() || superAdminRoleOpt.isEmpty()) {
            logger.error("Required roles not found for admin creation");
            return;
        }

        String encodedPassword = passwordEncoder.encode(adminPassword);
        User adminUser = User.builder()
                .username("Default Admin")
                .fullName("Default Admin")
                .gender(EUserGender.MALE)
                .nationalIdNumber("00000000000000")
                .phoneNumber("0788000000")
                .email(adminEmail)
                .password(encodedPassword)
                .occupation("System Administrator")
                .trackingLevel(EUserTrackingLevel.PROVINCE)
                .verifiedAt(LocalDateTime.now())
                .roles(Set.of(adminRoleOpt.get(), superAdminRoleOpt.get()))
                .build();

        userRepository.save(adminUser);
        logger.info("Backdoor Admin created");
    }
}