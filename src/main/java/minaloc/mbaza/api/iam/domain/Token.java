package minaloc.mbaza.api.iam.domain;

import java.time.LocalDateTime;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import minaloc.mbaza.api.common.domain.BasicEntity;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tokens")
@SQLDelete(sql = "UPDATE tokens SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?")
@SQLRestriction("deleted_at IS NULL")
public class Token extends BasicEntity {
    @Column(unique = true)
    public String token;

    public LocalDateTime revokedAt;

    public LocalDateTime expiredAt;

    @Column(name = "user_id", nullable = false)
    public String userId;
}
