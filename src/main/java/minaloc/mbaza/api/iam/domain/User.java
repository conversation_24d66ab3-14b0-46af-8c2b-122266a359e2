package minaloc.mbaza.api.iam.domain;

import java.time.LocalDateTime;
import java.util.Set;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import minaloc.mbaza.api.common.domain.BasicEntity;
import minaloc.mbaza.api.iam.enums.EUserGender;
import minaloc.mbaza.api.iam.enums.EUserTrackingLevel;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "users")
@SQLDelete(sql = "UPDATE users SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?")
@SQLRestriction("deleted_at IS NULL")
public class User extends BasicEntity {

    @Column(nullable = false)
    private String fullName;

    @Column(unique = true, nullable = false)
    private String nationalIdNumber;

    @Column(unique = true, nullable = false)
    private String phoneNumber;

    private String username;

    @Enumerated(EnumType.STRING)
    private EUserGender gender;

    private String email;

    @Column(nullable = false)
    private String password;

    private String occupation;

    @Enumerated(EnumType.STRING)
    private EUserTrackingLevel trackingLevel;

    private LocalDateTime verifiedAt;

    @OneToMany(mappedBy = "user")
    private Set<Token> tokens;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    public Organization organization;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "user_roles",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles;
}