package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.domain.IamUserDetails;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.exceptions.UnauthorizedException;
import minaloc.mbaza.api.common.interfaces.IamUserDetailsService;
import minaloc.mbaza.api.common.services.JwtService;
import minaloc.mbaza.api.iam.domain.Token;
import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.repositories.TokenRepository;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
public class LoginUseCase {

    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final TokenRepository tokenRepository;
    private final IamUserDetailsService iamUserDetailsService;

    public LoginDTO.Output execute(LoginDTO.Input loginRequestDTO) {
        IamUserDetails user = findVerifiedUser(loginRequestDTO.email());
        validatePassword(loginRequestDTO.password(), user);
        var token = generateToken(user, loginRequestDTO.email());
        return new LoginDTO.Output(token);
    }

    private void validatePassword(String inputPassword, IamUserDetails user) {
        if (!passwordEncoder.matches(inputPassword, user.getPassword())) {
            throw new UnauthorizedException("Invalid credentials");
        }
    }

    private String generateToken(IamUserDetails user, String identifier) {
        String jwtToken = jwtService.generateToken(user);
        deleteAllUserTokens(user.getId());
        saveUserToken(user, jwtToken, identifier);
        return jwtToken;
    }

    private IamUserDetails findVerifiedUser(String username) {
        IamUserDetails user = iamUserDetailsService.findUserByUsername(username);
        if (user == null) {
            throw new BadRequestException("Invalid credentials");
        }
        if (!user.isEnabled()) {
            throw new UnauthorizedException("Invalid credentials");
        }
        return user;
    }

    private void deleteAllUserTokens(String userId) {
        tokenRepository.deleteAllTokensByUserId(userId);
    }

    private void saveUserToken(IamUserDetails user, String jwtToken, String username) {
        Token token = new Token();
        token.userId = user.getId();
        token.token = jwtToken;
        tokenRepository.save(token);
    }
}
