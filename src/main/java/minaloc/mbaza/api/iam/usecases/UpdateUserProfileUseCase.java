package minaloc.mbaza.api.iam.usecases;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.common.utils.SecurityUtils;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.UpdateUserProfileDTO;
import minaloc.mbaza.api.iam.repositories.UserRepository;

@Service
@RequiredArgsConstructor
@Transactional
public class UpdateUserProfileUseCase {

    private final UserRepository userRepository;

    public void execute(UpdateUserProfileDTO.Input dto) {
        var currentUser = SecurityUtils.getCurrentUser();
        assert currentUser != null;

        User user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new BadRequestException("User not found"));

        if (userRepository.existsByEmailAndIdNot(dto.email(), user.getId())) {
            throw new BadRequestException("User with this email already exists");
        }

        if (userRepository.existsByPhoneNumberAndIdNot(dto.phoneNumber(), user.getId())) {
            throw new BadRequestException("Phone number already exists");
        }

        user.setUsername(dto.username());
        user.setEmail(dto.email());
        user.setFullName(dto.fullName());
        user.setPhoneNumber(dto.phoneNumber());
        user.setGender(dto.gender());

        userRepository.save(user);
    }
}
