package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.utils.SecurityUtils;
import minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Users.USERS;

@Service
@RequiredArgsConstructor

public class GetUserProfileUseCase {

    private final DSLContext dsl;

    public ViewUserProfileDTO.Output execute() {
        var currentUser = SecurityUtils.getCurrentUser();

        assert currentUser != null;
        return dsl.select(
                        USERS.ID,
                        USERS.EMAIL,
                        USERS.GENDER,
                        USERS.FULL_NAME,
                        USERS.NATIONAL_ID_NUMBER,
                        USERS.PHONE_NUMBER,
                        USERS.USERNAME
                )
                .from(USERS)
                .where(USERS.ID.eq(currentUser.getId()).and(USERS.IS_ACTIVE.eq(true)))
                .fetchOneInto(ViewUserProfileDTO.Output.class);
    }
}
