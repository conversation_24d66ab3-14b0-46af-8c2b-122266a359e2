package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.utils.SecurityUtils;
import minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Users.USERS;

@Service
@RequiredArgsConstructor

public class GetUserProfileUseCase {

    private final DSLContext dsl;

    public ViewUserProfileDTO.Output execute() {
        var currentUser = SecurityUtils.getCurrentUser();

        assert currentUser != null;
        return dsl.select(
                        USERS.EMAIL,
                        USERS.USERNAME,
                        USERS.FULL_NAME,
                        USERS.GENDER,
                        USERS.PHONE_NUMBER,
                        USERS.NATIONAL_ID_NUMBER
                )
                .from(USERS)
                .where(USERS.ID.eq(currentUser.getId()).and(USERS.IS_ACTIVE.eq(true)))
                .fetchOneInto(ViewUserProfileDTO.Output.class);
    }
}
