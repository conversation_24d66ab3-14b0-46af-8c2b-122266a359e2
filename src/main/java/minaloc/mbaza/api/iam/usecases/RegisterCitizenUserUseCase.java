package minaloc.mbaza.api.iam.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.enums.GlobalRolesConstants;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO;
import minaloc.mbaza.api.iam.repositories.IamUserValidationJooqRepository;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Set;

@Service
@Transactional
@RequiredArgsConstructor
public class RegisterCitizenUserUseCase {

    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final IamUserValidationJooqRepository userValidationRepository;

    public void execute(RegisterCitizenDTO.Input dto) {
        validateEmailOrPhoneNumberExists(dto.email(), dto.phoneNumber());

        var role = getCitizenRole();

        User user = new User();
        user.setFullName(dto.fullName());
        user.setPhoneNumber(dto.phoneNumber());
        user.setNationalIdNumber(dto.nationalIdNumber());
        user.setEmail(dto.email());
        user.setPassword(passwordEncoder.encode(dto.password()));
        // TODO: Set the active status to false when notifications are implemented
        user.setVerifiedAt(LocalDateTime.now());
        user.setGender(dto.gender());
        user.setRoles(Set.of(role));
        userRepository.save(user);
    }

    private void validateEmailOrPhoneNumberExists(String email, String phoneNumber) {
        if (email != null && userValidationRepository.existsByEmail(email)) {
            throw new BadRequestException("Email already exists");
        }
        if (phoneNumber != null && userValidationRepository.existsByPhoneNumber(phoneNumber)) {
            throw new BadRequestException("Phone number already exists");
        }
    }

    private Role getCitizenRole() {
        return roleRepository.findByName(GlobalRolesConstants.CITIZEN)
                .orElseThrow(() -> new BadRequestException("Citizen role not found"));
    }
}
