package minaloc.mbaza.api.iam.usecases;

import java.time.LocalDateTime;
import java.util.Set;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.enums.GlobalRolesConstants;
import minaloc.mbaza.api.iam.domain.Role;
import minaloc.mbaza.api.iam.domain.User;
import minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO;
import minaloc.mbaza.api.iam.repositories.RoleRepository;
import minaloc.mbaza.api.iam.repositories.UserRepository;

@Service
@Transactional
@RequiredArgsConstructor
public class RegisterCitizenUserUseCase {

    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    public void execute(RegisterCitizenDTO.Input dto) {
        validateEmailOrPhoneNumberExists(dto.email(), dto.phoneNumber());

        var role = getCitizenRole();

        User user = new User();
        user.setFullName(dto.fullName());
        user.setPhoneNumber(dto.phoneNumber());
        user.setNationalIdNumber(dto.nationalIdNumber());
        user.setEmail(dto.email());
        user.setPassword(passwordEncoder.encode(dto.password()));
        // TODO: Set the active status to false when notifications are implemented
        user.setVerifiedAt(LocalDateTime.now());
        user.setGender(dto.gender());
        user.setRoles(Set.of(role));
        userRepository.save(user);
    }

    private void validateEmailOrPhoneNumberExists(String email, String phoneNumber) {
        if (email != null && userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("Email already exists");
        }
        if (phoneNumber != null && userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new IllegalArgumentException("Phone number already exists");
        }
    }

    private Role getCitizenRole() {
        return roleRepository.findByName(GlobalRolesConstants.CITIZEN)
                .orElseThrow(() -> new IllegalArgumentException("Citizen role not found"));
    }
}
