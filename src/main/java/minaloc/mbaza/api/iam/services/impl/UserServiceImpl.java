package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.UpdateUserProfileDTO;
import minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO;
import minaloc.mbaza.api.iam.services.UserService;
import minaloc.mbaza.api.iam.usecases.GetUserProfileUseCase;
import minaloc.mbaza.api.iam.usecases.UpdateUserProfileUseCase;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    private final GetUserProfileUseCase getUserProfileUseCase;
    private final UpdateUserProfileUseCase updateUserProfileUseCase;

    @Override
    public ViewUserProfileDTO.Output getUserProfile() {
        return getUserProfileUseCase.execute();
    }

    @Override
    public void updateUserProfile(UpdateUserProfileDTO.Input dto) {
        updateUserProfileUseCase.execute(dto);
    }
}
