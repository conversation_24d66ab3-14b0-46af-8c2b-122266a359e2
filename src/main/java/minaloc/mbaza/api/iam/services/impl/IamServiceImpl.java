package minaloc.mbaza.api.iam.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.iam.dtos.LoginDTO;
import minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO;
import minaloc.mbaza.api.iam.services.IamService;
import minaloc.mbaza.api.iam.usecases.LoginUseCase;
import minaloc.mbaza.api.iam.usecases.RegisterCitizenUserUseCase;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IamServiceImpl implements IamService {

    private final RegisterCitizenUserUseCase registerCitizenUserUseCase;
    private final LoginUseCase loginUseCase;

    @Override
    public void registerCitizen(RegisterCitizenDTO.Input dto) {
        registerCitizenUserUseCase.execute(dto);
    }

    @Override
    public LoginDTO.Output login(LoginDTO.Input dto) {
        return loginUseCase.execute(dto);
    }

}
