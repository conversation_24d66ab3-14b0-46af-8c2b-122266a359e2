package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.services.LocationSearchService;
import minaloc.mbaza.api.location.usecases.SearchLocationHierarchyUseCase;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class LocationSearchServiceImpl implements LocationSearchService {
    
    private final SearchLocationHierarchyUseCase searchLocationHierarchyUseCase;
    
    @Override
    public List<LocationHierarchyDTO> searchLocationHierarchy(String locationName, String locationType) {
        return searchLocationHierarchyUseCase.execute(locationName, locationType);
    }
}
