package minaloc.mbaza.api.location.services;

import minaloc.mbaza.api.location.dtos.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

import java.util.List;

public interface LocationService {
    List<ViewProvinceDTO.Output> getAllProvinces();

    PagedModel<ViewDistrictDTO.Output> getDistrictsByProvinceId(String provinceId, Pageable pageable);

    PagedModel<ViewSectorDTO.Output> getSectorsByDistrictId(String districtId, Pageable pageable);

    PagedModel<ViewCellDTO.Output> getCellsBySectorId(String sectorId, Pageable pageable);

    PagedModel<ViewVillageDTO.Output> getVillagesByCellId(String cellId, Pageable pageable);

}
