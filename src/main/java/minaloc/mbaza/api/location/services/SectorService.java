package minaloc.mbaza.api.location.services;

import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface SectorService {
    Page<SectorDTO> getAllSectors(Pageable pageable);
    
    Page<CellDTO> getCellsBySectorName(String sectorName, Pageable pageable);
}
