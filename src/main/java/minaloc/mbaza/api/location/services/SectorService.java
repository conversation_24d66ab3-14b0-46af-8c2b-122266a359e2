package minaloc.mbaza.api.location.services;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;

public interface SectorService {
    PagedModel<ViewSectorDTO.Output> getAllSectors(Pageable pageable);

    PagedModel<ViewCellDTO.Output> getCellsBySectorName(String sectorName, Pageable pageable);
}
