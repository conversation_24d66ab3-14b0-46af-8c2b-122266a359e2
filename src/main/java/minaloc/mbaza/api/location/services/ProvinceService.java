package minaloc.mbaza.api.location.services;

import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ProvinceService {
    List<ProvinceDTO> getAllProvinces();
    
    Page<DistrictDTO> getDistrictsByProvinceName(String provinceName, Pageable pageable);
}
