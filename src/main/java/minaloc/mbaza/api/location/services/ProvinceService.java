package minaloc.mbaza.api.location.services;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;

public interface ProvinceService {
    List<ViewProvinceDTO.Output> getAllProvinces();

    PagedModel<ViewDistrictDTO.Output> getDistrictsByProvinceId(Long provinceId, Pageable pageable);
}
