package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.VillageService;
import minaloc.mbaza.api.location.usecases.GetAllVillagesUseCase;

@Service
@RequiredArgsConstructor
public class VillageServiceImpl implements VillageService {

    private final GetAllVillagesUseCase getAllVillagesUseCase;

    @Override
    public PagedModel<ViewVillageDTO.Output> getAllVillages(Pageable pageable) {
        return getAllVillagesUseCase.execute(pageable);
    }
}
