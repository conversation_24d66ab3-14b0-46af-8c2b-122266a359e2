package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import minaloc.mbaza.api.location.services.VillageService;
import minaloc.mbaza.api.location.usecases.GetAllVillages;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VillageServiceImpl implements VillageService {
    
    private final GetAllVillages getAllVillagesUseCase;
    
    @Override
    public Page<VillageDTO> getAllVillages(Pageable pageable) {
        return getAllVillagesUseCase.execute(pageable);
    }
}
