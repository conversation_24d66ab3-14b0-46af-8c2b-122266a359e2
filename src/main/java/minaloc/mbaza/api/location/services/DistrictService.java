package minaloc.mbaza.api.location.services;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;

public interface DistrictService {
    PagedModel<ViewDistrictDTO.Output> getAllDistricts(Pageable pageable);

    PagedModel<ViewSectorDTO.Output> getSectorsByDistrictId(Long districtId, Pageable pageable);
}
