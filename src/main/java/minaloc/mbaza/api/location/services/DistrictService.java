package minaloc.mbaza.api.location.services;

import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface DistrictService {
    Page<DistrictDTO> getAllDistricts(Pageable pageable);
    
    Page<SectorDTO> getSectorsByDistrictName(String districtName, Pageable pageable);
}
