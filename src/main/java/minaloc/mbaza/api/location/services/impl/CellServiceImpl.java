package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import minaloc.mbaza.api.location.services.CellService;
import minaloc.mbaza.api.location.usecases.GetAllCells;
import minaloc.mbaza.api.location.usecases.GetVillagesByCellNameUseCase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CellServiceImpl implements CellService {
    
    private final GetAllCells getAllCellsUseCase;
    private final GetVillagesByCellNameUseCase getVillagesByCellNameUseCase;
    
    @Override
    public Page<CellDTO> getAllCells(Pageable pageable) {
        return getAllCellsUseCase.execute(pageable);
    }
    
    @Override
    public Page<VillageDTO> getVillagesByCellName(String cellName, Pageable pageable) {
        return getVillagesByCellNameUseCase.execute(cellName, pageable);
    }
}
