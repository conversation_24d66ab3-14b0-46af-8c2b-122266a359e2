package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.CellService;
import minaloc.mbaza.api.location.usecases.GetAllCellsUseCase;
import minaloc.mbaza.api.location.usecases.GetVillagesByCellIdUseCase;

@Service
@RequiredArgsConstructor
public class CellServiceImpl implements CellService {

    private final GetAllCellsUseCase getAllCellsUseCase;
    private final GetVillagesByCellIdUseCase getVillagesByCellIdUseCase;

    @Override
    public PagedModel<ViewCellDTO.Output> getAllCells(Pageable pageable) {
        return getAllCellsUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewVillageDTO.Output> getVillagesByCellId(Long cellId, Pageable pageable) {
        return getVillagesByCellIdUseCase.execute(cellId, pageable);
    }
}
