package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.CellService;
import minaloc.mbaza.api.location.usecases.GetAllCellsJooqUseCase;
import minaloc.mbaza.api.location.usecases.GetVillagesByCellNameJooqUseCase;

@Service
@RequiredArgsConstructor
public class CellServiceImpl implements CellService {

    private final GetAllCellsJooqUseCase getAllCellsJooqUseCase;
    private final GetVillagesByCellNameJooqUseCase getVillagesByCellNameJooqUseCase;

    @Override
    public PagedModel<ViewCellDTO.Output> getAllCells(Pageable pageable) {
        return getAllCellsJooqUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewVillageDTO.Output> getVillagesByCellName(String cellName, Pageable pageable) {
        return getVillagesByCellNameJooqUseCase.execute(cellName, pageable);
    }
}
