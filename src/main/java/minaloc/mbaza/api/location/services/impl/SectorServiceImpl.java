package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.services.SectorService;
import minaloc.mbaza.api.location.usecases.GetAllSectors;
import minaloc.mbaza.api.location.usecases.GetCellsBySectorNameUseCase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SectorServiceImpl implements SectorService {
    
    private final GetAllSectors getAllSectorsUseCase;
    private final GetCellsBySectorNameUseCase getCellsBySectorNameUseCase;
    
    @Override
    public Page<SectorDTO> getAllSectors(Pageable pageable) {
        return getAllSectorsUseCase.execute(pageable);
    }
    
    @Override
    public Page<CellDTO> getCellsBySectorName(String sectorName, Pageable pageable) {
        return getCellsBySectorNameUseCase.execute(sectorName, pageable);
    }
}
