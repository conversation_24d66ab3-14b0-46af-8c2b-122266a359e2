package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.SectorService;
import minaloc.mbaza.api.location.usecases.GetAllSectorsJooqUseCase;
import minaloc.mbaza.api.location.usecases.GetCellsBySectorNameJooqUseCase;

@Service
@RequiredArgsConstructor
public class SectorServiceImpl implements SectorService {

    private final GetAllSectorsJooqUseCase getAllSectorsJooqUseCase;
    private final GetCellsBySectorNameJooqUseCase getCellsBySectorNameJooqUseCase;

    @Override
    public PagedModel<ViewSectorDTO.Output> getAllSectors(Pageable pageable) {
        return getAllSectorsJooqUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewCellDTO.Output> getCellsBySectorName(String sectorName, Pageable pageable) {
        return getCellsBySectorNameJooqUseCase.execute(sectorName, pageable);
    }
}
