package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.SectorService;
import minaloc.mbaza.api.location.usecases.GetAllSectorsUseCase;
import minaloc.mbaza.api.location.usecases.GetCellsBySectorIdUseCase;

@Service
@RequiredArgsConstructor
public class SectorServiceImpl implements SectorService {

    private final GetAllSectorsUseCase getAllSectorsUseCase;
    private final GetCellsBySectorIdUseCase getCellsBySectorIdUseCase;

    @Override
    public PagedModel<ViewSectorDTO.Output> getAllSectors(Pageable pageable) {
        return getAllSectorsUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewCellDTO.Output> getCellsBySectorId(Long sectorId, Pageable pageable) {
        return getCellsBySectorIdUseCase.execute(sectorId, pageable);
    }
}
