package minaloc.mbaza.api.location.services.impl;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;
import minaloc.mbaza.api.location.usecases.GetAllProvincesJooqUseCase;
import minaloc.mbaza.api.location.usecases.GetDistrictsByProvinceNameJooqUseCase;

@Service
@RequiredArgsConstructor
public class ProvinceServiceImpl implements ProvinceService {

    private final GetAllProvincesJooqUseCase getAllProvincesJooqUseCase;
    private final GetDistrictsByProvinceNameJooqUseCase getDistrictsByProvinceNameJooqUseCase;

    @Override
    public List<ViewProvinceDTO.Output> getAllProvinces() {
        return getAllProvincesJooqUseCase.execute();
    }

    @Override
    public PagedModel<ViewDistrictDTO.Output> getDistrictsByProvinceName(String provinceName, Pageable pageable) {
        return getDistrictsByProvinceNameJooqUseCase.execute(provinceName, pageable);
    }
}
