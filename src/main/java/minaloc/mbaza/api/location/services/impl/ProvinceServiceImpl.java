package minaloc.mbaza.api.location.services.impl;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;
import minaloc.mbaza.api.location.usecases.GetAllProvincesUseCase;
import minaloc.mbaza.api.location.usecases.GetDistrictsByProvinceIdUseCase;

@Service
@RequiredArgsConstructor
public class ProvinceServiceImpl implements ProvinceService {

    private final GetAllProvincesUseCase getAllProvincesUseCase;
    private final GetDistrictsByProvinceIdUseCase getDistrictsByProvinceIdUseCase;

    @Override
    public List<ViewProvinceDTO.Output> getAllProvinces() {
        return getAllProvincesUseCase.execute();
    }

    @Override
    public PagedModel<ViewDistrictDTO.Output> getDistrictsByProvinceId(Long provinceId, Pageable pageable) {
        return getDistrictsByProvinceIdUseCase.execute(provinceId, pageable);
    }
}
