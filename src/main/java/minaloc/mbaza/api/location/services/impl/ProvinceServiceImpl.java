package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;
import minaloc.mbaza.api.location.usecases.GetAllProvincesUseCase;
import minaloc.mbaza.api.location.usecases.GetDistrictsByProvinceNameUseCase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ProvinceServiceImpl implements ProvinceService {
    
    private final GetAllProvincesUseCase getAllProvincesUseCase;
    private final GetDistrictsByProvinceNameUseCase getDistrictsByProvinceNameUseCase;
    
    @Override
    public List<ProvinceDTO> getAllProvinces() {
        return getAllProvincesUseCase.execute();
    }
    
    @Override
    public Page<DistrictDTO> getDistrictsByProvinceName(String provinceName, Pageable pageable) {
        return getDistrictsByProvinceNameUseCase.execute(provinceName, pageable);
    }
}
