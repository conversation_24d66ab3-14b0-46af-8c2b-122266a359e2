package minaloc.mbaza.api.location.services;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;

import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;

public interface CellService {
    PagedModel<ViewCellDTO.Output> getAllCells(Pageable pageable);

    PagedModel<ViewVillageDTO.Output> getVillagesByCellName(String cellName, Pageable pageable);
}
