package minaloc.mbaza.api.location.services;

import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CellService {
    Page<CellDTO> getAllCells(Pageable pageable);
    
    Page<VillageDTO> getVillagesByCellName(String cellName, Pageable pageable);
}
