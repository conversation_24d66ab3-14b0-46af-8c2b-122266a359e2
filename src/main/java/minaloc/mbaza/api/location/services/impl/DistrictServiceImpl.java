package minaloc.mbaza.api.location.services.impl;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;
import minaloc.mbaza.api.location.usecases.GetAllDistricts;
import minaloc.mbaza.api.location.usecases.GetSectorsByDistrictNameUseCase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DistrictServiceImpl implements DistrictService {
    
    private final GetAllDistricts getAllDistrictsUseCase;
    private final GetSectorsByDistrictNameUseCase getSectorsByDistrictNameUseCase;
    
    @Override
    public Page<DistrictDTO> getAllDistricts(Pageable pageable) {
        return getAllDistrictsUseCase.execute(pageable);
    }
    
    @Override
    public Page<SectorDTO> getSectorsByDistrictName(String districtName, Pageable pageable) {
        return getSectorsByDistrictNameUseCase.execute(districtName, pageable);
    }
}
