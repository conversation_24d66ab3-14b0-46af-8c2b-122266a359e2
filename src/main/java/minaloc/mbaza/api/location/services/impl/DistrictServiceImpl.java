package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;
import minaloc.mbaza.api.location.usecases.GetAllDistrictsJooqUseCase;
import minaloc.mbaza.api.location.usecases.GetSectorsByDistrictNameJooqUseCase;

@Service
@RequiredArgsConstructor
public class DistrictServiceImpl implements DistrictService {

    private final GetAllDistrictsJooqUseCase getAllDistrictsJooqUseCase;
    private final GetSectorsByDistrictNameJooqUseCase getSectorsByDistrictNameJooqUseCase;

    @Override
    public PagedModel<ViewDistrictDTO.Output> getAllDistricts(Pageable pageable) {
        return getAllDistrictsJooqUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewSectorDTO.Output> getSectorsByDistrictName(String districtName, Pageable pageable) {
        return getSectorsByDistrictNameJooqUseCase.execute(districtName, pageable);
    }
}
