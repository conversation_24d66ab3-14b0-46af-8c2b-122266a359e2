package minaloc.mbaza.api.location.services.impl;

import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;
import minaloc.mbaza.api.location.usecases.GetAllDistrictsUseCase;
import minaloc.mbaza.api.location.usecases.GetSectorsByDistrictIdUseCase;

@Service
@RequiredArgsConstructor
public class DistrictServiceImpl implements DistrictService {

    private final GetAllDistrictsUseCase getAllDistrictsUseCase;
    private final GetSectorsByDistrictIdUseCase getSectorsByDistrictIdUseCase;

    @Override
    public PagedModel<ViewDistrictDTO.Output> getAllDistricts(Pageable pageable) {
        return getAllDistrictsUseCase.execute(pageable);
    }

    @Override
    public PagedModel<ViewSectorDTO.Output> getSectorsByDistrictId(Long districtId, Pageable pageable) {
        return getSectorsByDistrictIdUseCase.execute(districtId, pageable);
    }
}
