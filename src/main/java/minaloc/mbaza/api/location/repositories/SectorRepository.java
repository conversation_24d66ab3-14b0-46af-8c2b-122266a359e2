package minaloc.mbaza.api.location.repositories;

import minaloc.mbaza.api.location.domains.Sector;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface SectorRepository extends JpaRepository<Sector, String> {
    List<Sector> findByDistrict_NameIgnoreCase(String districtName);

    Page<Sector> findByDistrict_NameIgnoreCase(String districtName, Pageable pageable);

    List<Sector> findAllByNameIgnoreCase(String name);

}