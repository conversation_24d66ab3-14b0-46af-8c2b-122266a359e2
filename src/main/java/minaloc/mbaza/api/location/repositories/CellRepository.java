package minaloc.mbaza.api.location.repositories;

import minaloc.mbaza.api.location.domains.Cell;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CellRepository extends JpaRepository<Cell, String> {

    List<Cell> findBySector_NameIgnoreCase(String sectorName);

    Page<Cell> findBySector_NameIgnoreCase(String sectorName, Pageable pageable);

    List<Cell> findAllByNameIgnoreCase(String name);
}
