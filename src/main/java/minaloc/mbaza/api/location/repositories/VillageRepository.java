package minaloc.mbaza.api.location.repositories;

import minaloc.mbaza.api.location.domains.Village;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface VillageRepository extends JpaRepository<Village, String> {
    List<Village> findByCell_NameIgnoreCase(String cellName);

    Page<Village> findByCell_NameIgnoreCase(String cellName, Pageable pageable);

    List<Village> findByNameIgnoreCase(String villageName);

    List<Village> findAllByNameIgnoreCase(String locationName);
}
