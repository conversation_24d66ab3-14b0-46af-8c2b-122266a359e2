package minaloc.mbaza.api.location.repositories;

import minaloc.mbaza.api.location.domains.District;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface DistrictRepository extends JpaRepository<District, String> {
    List<District> findByProvince_NameIgnoreCase(String provinceName);

    Page<District> findByProvince_NameIgnoreCase(String provinceName, Pageable pageable);

    List<District> findAllByNameIgnoreCase(String name);
}