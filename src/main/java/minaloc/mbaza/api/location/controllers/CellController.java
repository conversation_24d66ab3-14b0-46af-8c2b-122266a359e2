package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.CellService;

@RestController
@RequestMapping("/api/v1/cells")
@RequiredArgsConstructor
@Tag(name = "Cells", description = "Endpoints for managing cells in Rwanda")
@Validated
@PublicEndpoint
public class CellController {

    private final CellService cellService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all cells",
            description = "Fetches a paginated list of all administrative cells in Rwanda.",
            parameters = {
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getAllCells(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewCellDTO.Output> cells = cellService.getAllCells(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Cells fetched successfully", cells));
    }

    @GetMapping(value = "/{cellName}/villages", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get villages by cell name",
            description = "Fetches a paginated list of villages associated with a given cell name.",
            parameters = {
                    @Parameter(name = "cellName", description = "The name of the cell (e.g., 'Kagarama').", required = true),
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getVillagesByCellName(
            @PathVariable("cellName") String cellName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewVillageDTO.Output> villages = cellService.getVillagesByCellName(cellName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Villages fetched successfully", villages));
    }
}
