package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.CellService;

@RestController
@RequestMapping("/api/v1/cells")
@RequiredArgsConstructor
@Tag(name = "Cells", description = "Endpoints for managing cells in Rwanda")
@Validated
@PublicEndpoint
public class CellController {

    private final CellService cellService;

    @GetMapping()
    @Operation(summary = "Get all cells")
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getAllCells(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewCellDTO.Output> cells = cellService.getAllCells(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Cells fetched successfully", cells));
    }

    @GetMapping("/{cellName}/villages")
    @Operation(summary = "Get villages by cell name")
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getVillagesByCellName(
            @PathVariable("cellName") String cellName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewVillageDTO.Output> villages = cellService.getVillagesByCellName(cellName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Villages fetched successfully", villages));
    }
}
