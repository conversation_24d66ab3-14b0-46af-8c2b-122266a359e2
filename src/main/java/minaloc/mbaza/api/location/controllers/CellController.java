package minaloc.mbaza.api.location.controllers;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.CellService;

@RestController
@RequestMapping("/api/v1/cells")
@RequiredArgsConstructor
@Tag(name = "Cells", description = "Endpoints for managing cells in Rwanda")
@Validated
@PublicEndpoint
public class CellController {

    private final CellService cellService;

    @Operation(summary = "Get all cells")
    @GetMapping
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getAllCells(
            @Valid @ParameterObject Pageable pageable
    ) {
        var cells = cellService.getAllCells(pageable);
        return GenericResponse.ok("Cells retrieved successfully", cells);
    }

    @Operation(summary = "Get villages by cell ID")
    @GetMapping("/{cellId}/villages")
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getVillagesByCellId(
            @PathVariable Long cellId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var villages = cellService.getVillagesByCellId(cellId, pageable);
        return GenericResponse.ok("Villages retrieved successfully", villages);
    }
}
