package minaloc.mbaza.api.location.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.services.LocationSearchService;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/locations")
@RequiredArgsConstructor
@Tag(name = "Location Search", description = "Endpoints for searching location hierarchy in Rwanda")
@Validated
@PublicEndpoint
public class LocationController {

    private final LocationSearchService locationSearchService;

    @GetMapping(name = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Search location hierarchy",
            description = "Searches for a location by name and returns its complete hierarchical path. " +
                    "For example, searching for a village returns the village along with its parent cell, sector, district, and province.",
            parameters = {
                    @Parameter(name = "locationName", description = "The name of the location to search for (can be village, cell, sector, district, or province name).", required = true),
                    @Parameter(name = "locationType", description = "Optional: Specify the type of location to search for (VILLAGE, CELL, SECTOR, DISTRICT, PROVINCE). If not specified, searches all types.", schema = @Schema(type = "string"))
            })
    public ResponseEntity<GenericResponse<List<LocationHierarchyDTO>>> searchLocationHierarchy(
            @RequestParam("locationName") String locationName,
            @RequestParam(value = "locationType", required = false) String locationType
    ) {
        List<LocationHierarchyDTO> hierarchyList = locationSearchService.searchLocationHierarchy(locationName, locationType);
        return ResponseEntity.ok(new GenericResponse<>("Location hierarchy fetched successfully", hierarchyList));
    }
}
