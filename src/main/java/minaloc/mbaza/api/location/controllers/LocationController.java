package minaloc.mbaza.api.location.controllers;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.services.LocationSearchService;

@RestController
@RequestMapping("/api/v1/locations")
@RequiredArgsConstructor
@Tag(name = "Location Search", description = "Endpoints for searching location hierarchy in Rwanda")
@Validated
@PublicEndpoint
public class LocationController {

    private final LocationSearchService locationSearchService;

    @GetMapping("/search")
    @Operation(summary = "Search location hierarchy")
    public ResponseEntity<GenericResponse<List<LocationHierarchyDTO>>> searchLocationHierarchy(
            @RequestParam("locationName") String locationName,
            @RequestParam(value = "locationType", required = false) String locationType
    ) {
        List<LocationHierarchyDTO> hierarchyList = locationSearchService.searchLocationHierarchy(locationName, locationType);
        return ResponseEntity.ok(new GenericResponse<>("Location hierarchy fetched successfully", hierarchyList));
    }
}
