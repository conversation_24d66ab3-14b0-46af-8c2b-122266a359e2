package minaloc.mbaza.api.location.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.*;
import minaloc.mbaza.api.location.services.LocationService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/locations")
@RequiredArgsConstructor
@Tag(name = "Location", description = "Endpoints for managing locations in Rwanda")
@Validated
@PublicEndpoint
public class LocationController {

    private final LocationService locationService;

    @Operation(summary = "Get all provinces")
    @GetMapping("/provinces")
    public ResponseEntity<GenericResponse<List<ViewProvinceDTO.Output>>> getAllProvinces() {
        var provinces = locationService.getAllProvinces();
        return GenericResponse.ok("Provinces retrieved successfully", provinces);
    }

    @Operation(summary = "Get districts by province ID")
    @GetMapping("/districts/{provinceId}")
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getDistrictsByProvinceId(
            @PathVariable String provinceId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var districts = locationService.getDistrictsByProvinceId(provinceId, pageable);
        return GenericResponse.ok("Districts retrieved successfully", districts);
    }

    @Operation(summary = "Get sectors by district ID")
    @GetMapping("/sectors/{districtId}")
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getSectorsByDistrictId(
            @PathVariable String districtId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var sectors = locationService.getSectorsByDistrictId(districtId, pageable);
        return GenericResponse.ok("Sectors retrieved successfully", sectors);
    }

    @Operation(summary = "Get cells by sector ID")
    @GetMapping("/cells/{sectorId}")
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getCellsBySectorId(
            @PathVariable String sectorId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var cells = locationService.getCellsBySectorId(sectorId, pageable);
        return GenericResponse.ok("Cells retrieved successfully", cells);
    }

    @Operation(summary = "Get villages by cell ID")
    @GetMapping("/villages/{cellId}")
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getVillagesByCellId(
            @PathVariable String cellId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var villages = locationService.getVillagesByCellId(cellId, pageable);
        return GenericResponse.ok("Villages retrieved successfully", villages);
    }
}
