package minaloc.mbaza.api.location.controllers;

import java.util.List;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;

@RestController
@RequestMapping("/api/v1/provinces")
@RequiredArgsConstructor
@Tag(name = "Provinces", description = "Endpoints for managing provinces in Rwanda")
@Validated
@PublicEndpoint
public class ProvinceController {

    private final ProvinceService provinceService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all provinces", description = "Fetches a list of all administrative provinces in Rwanda.")
    public ResponseEntity<GenericResponse<List<ViewProvinceDTO.Output>>> getAllProvinces() {
        List<ViewProvinceDTO.Output> provinces = provinceService.getAllProvinces();
        return ResponseEntity.ok(new GenericResponse<>("Provinces fetched successfully", provinces));
    }

    @GetMapping(value = "/{provinceName}/districts", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get districts by province name",
            description = "Fetches districts associated with a given province name with pagination support.",
            parameters = {
                    @Parameter(name = "provinceName", description = "The name of the province (e.g., 'Kigali').", required = true),
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getDistrictsByProvinceName(
            @PathVariable("provinceName") String provinceName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewDistrictDTO.Output> districts = provinceService.getDistrictsByProvinceName(provinceName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Districts fetched successfully", districts));
    }
}
