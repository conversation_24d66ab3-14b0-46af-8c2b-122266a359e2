package minaloc.mbaza.api.location.controllers;

import java.util.List;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;

@RestController
@RequestMapping("/api/v1/provinces")
@RequiredArgsConstructor
@Tag(name = "Provinces", description = "Endpoints for managing provinces in Rwanda")
@Validated
@PublicEndpoint
public class ProvinceController {

    private final ProvinceService provinceService;

    @Operation(summary = "Get all provinces")
    @GetMapping
    public ResponseEntity<GenericResponse<List<ViewProvinceDTO.Output>>> getAllProvinces() {
        var provinces = provinceService.getAllProvinces();
        return GenericResponse.ok("Provinces retrieved successfully", provinces);
    }

    @Operation(summary = "Get districts by province name")
    @GetMapping("/{provinceName}/districts")
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getDistrictsByProvinceName(
            @PathVariable String provinceName,
            @Valid @ParameterObject Pageable pageable
    ) {
        var districts = provinceService.getDistrictsByProvinceName(provinceName, pageable);
        return GenericResponse.ok("Districts retrieved successfully", districts);
    }
}
