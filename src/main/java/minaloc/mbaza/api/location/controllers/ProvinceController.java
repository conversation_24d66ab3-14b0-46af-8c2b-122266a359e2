package minaloc.mbaza.api.location.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import minaloc.mbaza.api.location.services.ProvinceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/provinces")
@RequiredArgsConstructor
@Tag(name = "Provinces", description = "Endpoints for managing provinces in Rwanda")
@Validated
@PublicEndpoint
public class ProvinceController {

    private final ProvinceService provinceService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all provinces", description = "Fetches a list of all administrative provinces in Rwanda.")
    public ResponseEntity<GenericResponse<List<ProvinceDTO>>> getAllProvinces() {
        List<ProvinceDTO> provinces = provinceService.getAllProvinces();
        return ResponseEntity.ok(new GenericResponse<>("Provinces fetched successfully", provinces));
    }

    @GetMapping(value = "/{provinceName}/districts", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get districts by province name",
            description = "Fetches districts associated with a given province name with pagination support.",
            parameters = {
                    @Parameter(name = "provinceName", description = "The name of the province (e.g., 'Kigali').", required = true),
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<Page<DistrictDTO>>> getDistrictsByProvinceName(
            @PathVariable("provinceName") String provinceName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<DistrictDTO> districts = provinceService.getDistrictsByProvinceName(provinceName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Districts fetched successfully", districts));
    }
}
