package minaloc.mbaza.api.location.controllers;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;

@RestController
@RequestMapping("/api/v1/districts")
@RequiredArgsConstructor
@Tag(name = "Districts", description = "Endpoints for managing districts in Rwanda")
@Validated
@PublicEndpoint
public class DistrictController {

    private final DistrictService districtService;

    @Operation(summary = "Get all districts")
    @GetMapping
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getAllDistricts(
            @Valid @ParameterObject Pageable pageable
    ) {
        var districts = districtService.getAllDistricts(pageable);
        return GenericResponse.ok("Districts retrieved successfully", districts);
    }

    @Operation(summary = "Get sectors by district ID")
    @GetMapping("/{districtId}/sectors")
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getSectorsByDistrictId(
            @PathVariable Long districtId,
            @Valid @ParameterObject Pageable pageable
    ) {
        var sectors = districtService.getSectorsByDistrictId(districtId, pageable);
        return GenericResponse.ok("Sectors retrieved successfully", sectors);
    }
}
