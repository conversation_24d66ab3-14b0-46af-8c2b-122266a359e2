package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;

@RestController
@RequestMapping("/api/v1/districts")
@RequiredArgsConstructor
@Tag(name = "Districts", description = "Endpoints for managing districts in Rwanda")
@Validated
@PublicEndpoint
public class DistrictController {

    private final DistrictService districtService;

    @GetMapping()
    @Operation(summary = "Get all districts")
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getAllDistricts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewDistrictDTO.Output> districts = districtService.getAllDistricts(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Districts fetched successfully", districts));
    }

    @GetMapping("/{districtName}/sectors")
    @Operation(summary = "Get sectors by district name")
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getSectorsByDistrictName(
            @PathVariable("districtName") String districtName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewSectorDTO.Output> sectors = districtService.getSectorsByDistrictName(districtName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Sectors fetched successfully", sectors));
    }
}
