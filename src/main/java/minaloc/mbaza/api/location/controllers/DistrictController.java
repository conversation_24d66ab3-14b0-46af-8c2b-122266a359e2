package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.DistrictService;

@RestController
@RequestMapping("/api/v1/districts")
@RequiredArgsConstructor
@Tag(name = "Districts", description = "Endpoints for managing districts in Rwanda")
@Validated
@PublicEndpoint
public class DistrictController {

    private final DistrictService districtService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all districts",
            description = "Fetches a paginated list of all administrative districts in Rwanda.",
            parameters = {
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewDistrictDTO.Output>>> getAllDistricts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewDistrictDTO.Output> districts = districtService.getAllDistricts(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Districts fetched successfully", districts));
    }

    @GetMapping(value = "/{districtName}/sectors", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get sectors by district name",
            description = "Fetches a paginated list of sectors associated with a given district name.",
            parameters = {
                    @Parameter(name = "districtName", description = "The name of the district (e.g., 'Gasabo').", required = true),
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getSectorsByDistrictName(
            @PathVariable("districtName") String districtName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewSectorDTO.Output> sectors = districtService.getSectorsByDistrictName(districtName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Sectors fetched successfully", sectors));
    }
}
