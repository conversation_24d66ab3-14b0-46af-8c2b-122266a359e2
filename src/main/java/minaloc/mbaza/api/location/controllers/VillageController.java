package minaloc.mbaza.api.location.controllers;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.VillageService;

@RestController
@RequestMapping("/api/v1/villages")
@RequiredArgsConstructor
@Tag(name = "Villages", description = "Endpoints for managing villages in Rwanda")
@Validated
@PublicEndpoint
public class VillageController {

    private final VillageService villageService;

    @Operation(summary = "Get all villages")
    @GetMapping
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getAllVillages(
            @Valid @ParameterObject Pageable pageable
    ) {
        var villages = villageService.getAllVillages(pageable);
        return GenericResponse.ok("Villages retrieved successfully", villages);
    }
}
