package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.services.VillageService;

@RestController
@RequestMapping("/api/v1/villages")
@RequiredArgsConstructor
@Tag(name = "Villages", description = "Endpoints for managing villages in Rwanda")
@Validated
@PublicEndpoint
public class VillageController {

    private final VillageService villageService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all villages",
            description = "Fetches a paginated list of all administrative villages in Rwanda.",
            parameters = {
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<PagedModel<ViewVillageDTO.Output>>> getAllVillages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewVillageDTO.Output> villages = villageService.getAllVillages(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Villages fetched successfully", villages));
    }
}
