package minaloc.mbaza.api.location.controllers;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.services.SectorService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/sectors")
@RequiredArgsConstructor
@Tag(name = "Sectors", description = "Endpoints for managing sectors in Rwanda")
@Validated
@PublicEndpoint
public class SectorController {

    private final SectorService sectorService;

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get all sectors",
            description = "Fetches a paginated list of all administrative sectors in Rwanda.",
            parameters = {
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<Page<SectorDTO>>> getAllSectors(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<SectorDTO> sectors = sectorService.getAllSectors(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Sectors fetched successfully", sectors));
    }

    @GetMapping(value = "/{sectorName}/cells", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get cells by sector name",
            description = "Fetches a paginated list of cells associated with a given sector name.",
            parameters = {
                    @Parameter(name = "sectorName", description = "The name of the sector (e.g., 'Remera').", required = true),
                    @Parameter(name = "page", description = "Page number (0-indexed, default: 0)", schema = @Schema(type = "integer", defaultValue = "0")),
                    @Parameter(name = "size", description = "Number of records per page (default: 10)", schema = @Schema(type = "integer", defaultValue = "10"))
            })
    public ResponseEntity<GenericResponse<Page<CellDTO>>> getCellsBySectorName(
            @PathVariable("sectorName") String sectorName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<CellDTO> cells = sectorService.getCellsBySectorName(sectorName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Cells fetched successfully", cells));
    }
}
