package minaloc.mbaza.api.location.controllers;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.SectorService;

@RestController
@RequestMapping("/api/v1/sectors")
@RequiredArgsConstructor
@Tag(name = "Sectors", description = "Endpoints for managing sectors in Rwanda")
@Validated
@PublicEndpoint
public class SectorController {

    private final SectorService sectorService;

    @Operation(summary = "Get all sectors")
    @GetMapping
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getAllSectors(
            @Valid @ParameterObject Pageable pageable
    ) {
        var sectors = sectorService.getAllSectors(pageable);
        return GenericResponse.ok("Sectors retrieved successfully", sectors);
    }

    @Operation(summary = "Get cells by sector name")
    @GetMapping("/{sectorName}/cells")
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getCellsBySectorName(
            @PathVariable String sectorName,
            @Valid @ParameterObject Pageable pageable
    ) {
        var cells = sectorService.getCellsBySectorName(sectorName, pageable);
        return GenericResponse.ok("Cells retrieved successfully", cells);
    }
}
