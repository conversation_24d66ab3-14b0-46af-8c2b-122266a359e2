package minaloc.mbaza.api.location.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.common.annotations.PublicEndpoint;
import minaloc.mbaza.api.common.responses.GenericResponse;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.services.SectorService;

@RestController
@RequestMapping("/api/v1/sectors")
@RequiredArgsConstructor
@Tag(name = "Sectors", description = "Endpoints for managing sectors in Rwanda")
@Validated
@PublicEndpoint
public class SectorController {

    private final SectorService sectorService;

    @GetMapping()
    @Operation(summary = "Get all sectors")
    public ResponseEntity<GenericResponse<PagedModel<ViewSectorDTO.Output>>> getAllSectors(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewSectorDTO.Output> sectors = sectorService.getAllSectors(pageable);
        return ResponseEntity.ok(new GenericResponse<>("Sectors fetched successfully", sectors));
    }

    @GetMapping("/{sectorName}/cells")
    @Operation(summary = "Get cells by sector name")
    public ResponseEntity<GenericResponse<PagedModel<ViewCellDTO.Output>>> getCellsBySectorName(
            @PathVariable("sectorName") String sectorName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        PagedModel<ViewCellDTO.Output> cells = sectorService.getCellsBySectorName(sectorName, pageable);
        return ResponseEntity.ok(new GenericResponse<>("Cells fetched successfully", cells));
    }
}
