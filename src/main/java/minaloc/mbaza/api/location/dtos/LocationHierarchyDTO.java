package minaloc.mbaza.api.location.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationHierarchyDTO {
    private ViewProvinceDTO.Output province;
    private ViewDistrictDTO.Output district;
    private ViewSectorDTO.Output sector;
    private ViewCellDTO.Output cell;
    private ViewVillageDTO.Output village;
    private String searchedLocation;
    private String searchedLocationType;
    private String hierarchyPath;
}