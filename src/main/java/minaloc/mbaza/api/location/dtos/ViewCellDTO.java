package minaloc.mbaza.api.location.dtos;

import java.time.LocalDateTime;

public final class ViewCellDTO {
    
    private ViewCellDTO() {
        // Private constructor to prevent instantiation
    }
    
    public record Output(
        String id,
        String name,
        String code,
        String sectorName,
        String sectorCode,
        String districtName,
        String districtCode,
        String provinceName,
        String provinceCode,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {}
}
