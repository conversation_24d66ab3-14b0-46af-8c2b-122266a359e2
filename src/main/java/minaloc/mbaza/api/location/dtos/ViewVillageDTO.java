package minaloc.mbaza.api.location.dtos;

import java.time.LocalDateTime;

public final class ViewVillageDTO {
    
    private ViewVillageDTO() {
        // Private constructor to prevent instantiation
    }
    
    public record Output(
        String id,
        String name,
        String code,
        String cellName,
        String cellCode,
        String sectorName,
        String sectorCode,
        String districtName,
        String districtCode,
        String provinceName,
        String provinceCode,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {}
}
