package minaloc.mbaza.api.location.dtos;

import java.time.LocalDateTime;

public final class ViewDistrictDTO {
    
    private ViewDistrictDTO() {
        // Private constructor to prevent instantiation
    }
    
    public record Output(
        String id,
        String name,
        String code,
        String provinceName,
        String provinceCode,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {}
}
