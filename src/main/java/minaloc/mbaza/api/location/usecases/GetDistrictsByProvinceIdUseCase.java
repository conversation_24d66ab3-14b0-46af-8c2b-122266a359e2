package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;

@Service
@AllArgsConstructor
public class GetDistrictsByProvinceIdUseCase {

    private final DSLContext dsl;

    public PagedModel<ViewDistrictDTO.Output> execute(String provinceId, Pageable pageable) {
        var query = dsl.select(
                        DISTRICTS.ID,
                        DISTRICTS.NAME
                )
                .from(DISTRICTS)
                .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
                .where(DISTRICTS.PROVINCE_ID.eq(provinceId));

        return JooqExtensions.fetchInto(query, ViewDistrictDTO.Output.class, pageable);
    }
}
