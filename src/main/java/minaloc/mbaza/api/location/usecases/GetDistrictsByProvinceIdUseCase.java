package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import java.util.List;

import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;

@Service
@AllArgsConstructor
public class GetDistrictsByProvinceIdUseCase {
    
    private final DSLContext dsl;
    
    public List<ViewDistrictDTO.Output> execute(Long provinceId) {
        return dsl.select(
                DISTRICTS.ID,
                DISTRICTS.NAME,
                DISTRICTS.CODE,
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                DISTRICTS.CREATED_AT,
                DISTRICTS.UPDATED_AT
            )
            .from(DISTRICTS)
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(PROVINCES.ID.eq(provinceId))
            .orderBy(DISTRICTS.NAME.asc())
            .fetchInto(ViewDistrictDTO.Output.class);
    }
    
    public PagedModel<ViewDistrictDTO.Output> execute(Long provinceId, Pageable pageable) {
        var query = dsl.select(
                DISTRICTS.ID,
                DISTRICTS.NAME,
                DISTRICTS.CODE,
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                DISTRICTS.CREATED_AT,
                DISTRICTS.UPDATED_AT
            )
            .from(DISTRICTS)
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(PROVINCES.ID.eq(provinceId));
            
        return JooqExtensions.fetchInto(query, ViewDistrictDTO.Output.class, pageable);
    }
}
