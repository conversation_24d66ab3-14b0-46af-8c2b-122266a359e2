package minaloc.mbaza.api.location.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Sector;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.SectorMapper;
import minaloc.mbaza.api.location.repositories.SectorRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Use case for retrieving sectors by district name.
 * This class is responsible for fetching sectors from the repository based on the district name and converting them to DTOs.
 */


@Service
@RequiredArgsConstructor
public class GetSectorsByDistrictNameUseCase {

    private final SectorRepository sectorRepository;
    private final SectorMapper sectorMapper;


    public List<SectorDTO> execute(String districtName) {
        List<Sector> sectors = sectorRepository.findByDistrict_NameIgnoreCase(districtName);


        try {
            return sectors.stream()
                    .map(sectorMapper::map)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping sectors for district: " + districtName, e);
        }
    }

    public Page<SectorDTO> execute(String districtName, Pageable pageable) {
        Page<Sector> sectorPage = sectorRepository.findByDistrict_NameIgnoreCase(districtName, pageable);
        if (sectorPage.isEmpty()) {
            throw new LocationServiceException("No sectors found for district name: " + districtName);
        }

        try {
            List<SectorDTO> sectorDTOs = sectorPage.getContent().stream()
                    .map(sectorMapper::map)
                    .toList();
            return new PageImpl<>(sectorDTOs, pageable, sectorPage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping paginated sectors for district: " + districtName, e);
        }
    }


}