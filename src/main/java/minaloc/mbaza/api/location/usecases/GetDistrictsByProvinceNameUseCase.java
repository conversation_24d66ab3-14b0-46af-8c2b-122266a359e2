package minaloc.mbaza.api.location.usecases;


import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.District;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.DistrictMapper;
import minaloc.mbaza.api.location.repositories.DistrictRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Use case for retrieving districts by province name.
 * This class is responsible for fetching districts from the repository based on the province name and converting them to DTOs.
 */


@Service
@RequiredArgsConstructor
public class GetDistrictsByProvinceNameUseCase {

    private final DistrictRepository districtRepository;
    private final DistrictMapper districtMapper;

    public List<DistrictDTO> execute(String provinceName) {
        List<District> districts = districtRepository.findByProvince_NameIgnoreCase(provinceName);
        if (districts.isEmpty()) {
            throw new LocationServiceException("No districts found for province name: " + provinceName);
        }

        try {
            return districts.stream()
                    .map(districtMapper::map)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping districts for province: " + provinceName, e);
        }
    }

    public Page<DistrictDTO> execute(String provinceName, Pageable pageable) {
        Page<District> districtPage = districtRepository.findByProvince_NameIgnoreCase(provinceName, pageable);


        try {
            List<DistrictDTO> districtDTOs = districtPage.getContent().stream()
                    .map(districtMapper::map)
                    .collect(Collectors.toList());
            return new PageImpl<>(districtDTOs, pageable, districtPage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping paginated districts for province: " + provinceName, e);
        }
    }
}