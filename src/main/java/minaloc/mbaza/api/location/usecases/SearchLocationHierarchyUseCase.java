package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;
import static minaloc.mbaza.jooq.generated.tables.Villages.VILLAGES;

import java.util.ArrayList;
import java.util.List;

import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;

@Component
@RequiredArgsConstructor
public class SearchLocationHierarchyUseCase {

    private final DSLContext dsl;

    public List<LocationHierarchyDTO> execute(String locationName, String locationType) {
        if (locationName == null || locationName.trim().isEmpty()) {
            throw new LocationServiceException("Location name must be provided");
        }

        List<LocationHierarchyDTO> results = new ArrayList<>();

        // Search based on specified locationType
        if (locationType != null && !locationType.trim().isEmpty()) {
            switch (locationType.toUpperCase()) {
                case "VILLAGE" -> results.addAll(searchVillagesJooq(locationName));
                case "CELL" -> results.addAll(searchCellsJooq(locationName));
                case "SECTOR" -> results.addAll(searchSectorsJooq(locationName));
                case "DISTRICT" -> results.addAll(searchDistrictsJooq(locationName));
                case "PROVINCE" -> results.addAll(searchProvincesJooq(locationName));
                default -> throw new LocationServiceException("Invalid location type: " + locationType);
            }
        } else {
            // Search across all levels
            results.addAll(searchVillagesJooq(locationName));
            results.addAll(searchCellsJooq(locationName));
            results.addAll(searchSectorsJooq(locationName));
            results.addAll(searchDistrictsJooq(locationName));
            results.addAll(searchProvincesJooq(locationName));
        }

        return results;
    }

    private List<LocationHierarchyDTO> searchVillagesJooq(String name) {
        return dsl.select(
                VILLAGES.NAME.as("villageName"),
                VILLAGES.CODE.as("villageCode"),
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode")
            )
            .from(VILLAGES)
            .join(CELLS).on(VILLAGES.CELL_ID.eq(CELLS.ID))
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(VILLAGES.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("VILLAGE")
                    .village(new VillageDTO(record.get("villageName", String.class), record.get("villageCode", String.class)))
                    .cell(new CellDTO(record.get("cellName", String.class), record.get("cellCode", String.class)))
                    .sector(new SectorDTO(record.get("sectorName", String.class), record.get("sectorCode", String.class)))
                    .district(new DistrictDTO(record.get("districtName", String.class), record.get("districtCode", String.class)))
                    .province(new ProvinceDTO(record.get("provinceName", String.class), record.get("provinceCode", String.class)))
                    .hierarchyPath(String.format("%s > %s > %s > %s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class),
                            record.get("sectorName", String.class), record.get("cellName", String.class),
                            record.get("villageName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchCellsJooq(String name) {
        return dsl.select(
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode")
            )
            .from(CELLS)
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(CELLS.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("CELL")
                    .cell(new CellDTO(record.get("cellName", String.class), record.get("cellCode", String.class)))
                    .sector(new SectorDTO(record.get("sectorName", String.class), record.get("sectorCode", String.class)))
                    .district(new DistrictDTO(record.get("districtName", String.class), record.get("districtCode", String.class)))
                    .province(new ProvinceDTO(record.get("provinceName", String.class), record.get("provinceCode", String.class)))
                    .hierarchyPath(String.format("%s > %s > %s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class),
                            record.get("sectorName", String.class), record.get("cellName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchSectorsJooq(String name) {
        return new ArrayList<>(); // TODO: Implement JOOQ version
    }

    private List<LocationHierarchyDTO> searchDistrictsJooq(String name) {
        return new ArrayList<>(); // TODO: Implement JOOQ version
    }

    private List<LocationHierarchyDTO> searchProvincesJooq(String name) {
        return new ArrayList<>(); // TODO: Implement JOOQ version
    }
}
