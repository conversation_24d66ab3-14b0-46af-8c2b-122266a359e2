package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;
import static minaloc.mbaza.jooq.generated.tables.Villages.VILLAGES;

import java.util.ArrayList;
import java.util.List;

import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;

@Component
@RequiredArgsConstructor
public class SearchLocationHierarchyUseCase {

    private final DSLContext dsl;

    public List<LocationHierarchyDTO> execute(String locationName, String locationType) {
        if (locationName == null || locationName.trim().isEmpty()) {
            throw new LocationServiceException("Location name must be provided");
        }

        List<LocationHierarchyDTO> results = new ArrayList<>();

        // Search based on specified locationType
        if (locationType != null && !locationType.trim().isEmpty()) {
            switch (locationType.toUpperCase()) {
                case "VILLAGE" -> results.addAll(searchVillagesJooq(locationName));
                case "CELL" -> results.addAll(searchCellsJooq(locationName));
                case "SECTOR" -> results.addAll(searchSectorsJooq(locationName));
                case "DISTRICT" -> results.addAll(searchDistrictsJooq(locationName));
                case "PROVINCE" -> results.addAll(searchProvincesJooq(locationName));
                default -> throw new LocationServiceException("Invalid location type: " + locationType);
            }
        } else {
            // Search across all levels
            results.addAll(searchVillagesJooq(locationName));
            results.addAll(searchCellsJooq(locationName));
            results.addAll(searchSectorsJooq(locationName));
            results.addAll(searchDistrictsJooq(locationName));
            results.addAll(searchProvincesJooq(locationName));
        }

        return results;
    }

    private List<LocationHierarchyDTO> searchVillagesJooq(String name) {
        return dsl.select(
                VILLAGES.ID,
                VILLAGES.NAME.as("villageName"),
                VILLAGES.CODE.as("villageCode"),
                VILLAGES.CREATED_AT.as("villageCreatedAt"),
                VILLAGES.UPDATED_AT.as("villageUpdatedAt"),
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                CELLS.CREATED_AT.as("cellCreatedAt"),
                CELLS.UPDATED_AT.as("cellUpdatedAt"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                SECTORS.CREATED_AT.as("sectorCreatedAt"),
                SECTORS.UPDATED_AT.as("sectorUpdatedAt"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                DISTRICTS.CREATED_AT.as("districtCreatedAt"),
                DISTRICTS.UPDATED_AT.as("districtUpdatedAt"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                PROVINCES.CREATED_AT.as("provinceCreatedAt"),
                PROVINCES.UPDATED_AT.as("provinceUpdatedAt")
            )
            .from(VILLAGES)
            .join(CELLS).on(VILLAGES.CELL_ID.eq(CELLS.ID))
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(VILLAGES.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("VILLAGE")
                    .village(new ViewVillageDTO.Output(
                            record.get(VILLAGES.ID).toString(),
                            record.get("villageName", String.class),
                            record.get("villageCode", String.class),
                            record.get("cellName", String.class),
                            record.get("cellCode", String.class),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("villageCreatedAt", java.time.LocalDateTime.class),
                            record.get("villageUpdatedAt", java.time.LocalDateTime.class)))
                    .cell(new ViewCellDTO.Output(
                            record.get(CELLS.ID).toString(),
                            record.get("cellName", String.class),
                            record.get("cellCode", String.class),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("cellCreatedAt", java.time.LocalDateTime.class),
                            record.get("cellUpdatedAt", java.time.LocalDateTime.class)))
                    .sector(new ViewSectorDTO.Output(
                            record.get(SECTORS.ID).toString(),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("sectorCreatedAt", java.time.LocalDateTime.class),
                            record.get("sectorUpdatedAt", java.time.LocalDateTime.class)))
                    .district(new ViewDistrictDTO.Output(
                            record.get(DISTRICTS.ID).toString(),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("districtCreatedAt", java.time.LocalDateTime.class),
                            record.get("districtUpdatedAt", java.time.LocalDateTime.class)))
                    .province(new ViewProvinceDTO.Output(
                            record.get(PROVINCES.ID).toString(),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("provinceCreatedAt", java.time.LocalDateTime.class),
                            record.get("provinceUpdatedAt", java.time.LocalDateTime.class)))
                    .hierarchyPath(String.format("%s > %s > %s > %s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class),
                            record.get("sectorName", String.class), record.get("cellName", String.class),
                            record.get("villageName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchCellsJooq(String name) {
        return dsl.select(
                CELLS.ID,
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                CELLS.CREATED_AT.as("cellCreatedAt"),
                CELLS.UPDATED_AT.as("cellUpdatedAt"),
                SECTORS.ID.as("sectorId"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                SECTORS.CREATED_AT.as("sectorCreatedAt"),
                SECTORS.UPDATED_AT.as("sectorUpdatedAt"),
                DISTRICTS.ID.as("districtId"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                DISTRICTS.CREATED_AT.as("districtCreatedAt"),
                DISTRICTS.UPDATED_AT.as("districtUpdatedAt"),
                PROVINCES.ID.as("provinceId"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                PROVINCES.CREATED_AT.as("provinceCreatedAt"),
                PROVINCES.UPDATED_AT.as("provinceUpdatedAt")
            )
            .from(CELLS)
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(CELLS.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("CELL")
                    .cell(new ViewCellDTO.Output(
                            record.get(CELLS.ID).toString(),
                            record.get("cellName", String.class),
                            record.get("cellCode", String.class),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("cellCreatedAt", java.time.LocalDateTime.class),
                            record.get("cellUpdatedAt", java.time.LocalDateTime.class)))
                    .sector(new ViewSectorDTO.Output(
                            record.get("sectorId", Long.class).toString(),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("sectorCreatedAt", java.time.LocalDateTime.class),
                            record.get("sectorUpdatedAt", java.time.LocalDateTime.class)))
                    .district(new ViewDistrictDTO.Output(
                            record.get("districtId", Long.class).toString(),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("districtCreatedAt", java.time.LocalDateTime.class),
                            record.get("districtUpdatedAt", java.time.LocalDateTime.class)))
                    .province(new ViewProvinceDTO.Output(
                            record.get("provinceId", Long.class).toString(),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("provinceCreatedAt", java.time.LocalDateTime.class),
                            record.get("provinceUpdatedAt", java.time.LocalDateTime.class)))
                    .hierarchyPath(String.format("%s > %s > %s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class),
                            record.get("sectorName", String.class), record.get("cellName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchSectorsJooq(String name) {
        return dsl.select(
                SECTORS.ID,
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                SECTORS.CREATED_AT.as("sectorCreatedAt"),
                SECTORS.UPDATED_AT.as("sectorUpdatedAt"),
                DISTRICTS.ID.as("districtId"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                DISTRICTS.CREATED_AT.as("districtCreatedAt"),
                DISTRICTS.UPDATED_AT.as("districtUpdatedAt"),
                PROVINCES.ID.as("provinceId"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                PROVINCES.CREATED_AT.as("provinceCreatedAt"),
                PROVINCES.UPDATED_AT.as("provinceUpdatedAt")
            )
            .from(SECTORS)
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(SECTORS.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("SECTOR")
                    .sector(new ViewSectorDTO.Output(
                            record.get(SECTORS.ID).toString(),
                            record.get("sectorName", String.class),
                            record.get("sectorCode", String.class),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("sectorCreatedAt", java.time.LocalDateTime.class),
                            record.get("sectorUpdatedAt", java.time.LocalDateTime.class)))
                    .district(new ViewDistrictDTO.Output(
                            record.get("districtId", Long.class).toString(),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("districtCreatedAt", java.time.LocalDateTime.class),
                            record.get("districtUpdatedAt", java.time.LocalDateTime.class)))
                    .province(new ViewProvinceDTO.Output(
                            record.get("provinceId", Long.class).toString(),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("provinceCreatedAt", java.time.LocalDateTime.class),
                            record.get("provinceUpdatedAt", java.time.LocalDateTime.class)))
                    .hierarchyPath(String.format("%s > %s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class),
                            record.get("sectorName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchDistrictsJooq(String name) {
        return dsl.select(
                DISTRICTS.ID,
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                DISTRICTS.CREATED_AT.as("districtCreatedAt"),
                DISTRICTS.UPDATED_AT.as("districtUpdatedAt"),
                PROVINCES.ID.as("provinceId"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                PROVINCES.CREATED_AT.as("provinceCreatedAt"),
                PROVINCES.UPDATED_AT.as("provinceUpdatedAt")
            )
            .from(DISTRICTS)
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(DISTRICTS.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("DISTRICT")
                    .district(new ViewDistrictDTO.Output(
                            record.get(DISTRICTS.ID).toString(),
                            record.get("districtName", String.class),
                            record.get("districtCode", String.class),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("districtCreatedAt", java.time.LocalDateTime.class),
                            record.get("districtUpdatedAt", java.time.LocalDateTime.class)))
                    .province(new ViewProvinceDTO.Output(
                            record.get("provinceId", Long.class).toString(),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("provinceCreatedAt", java.time.LocalDateTime.class),
                            record.get("provinceUpdatedAt", java.time.LocalDateTime.class)))
                    .hierarchyPath(String.format("%s > %s",
                            record.get("provinceName", String.class), record.get("districtName", String.class)))
                    .build());
    }

    private List<LocationHierarchyDTO> searchProvincesJooq(String name) {
        return dsl.select(
                PROVINCES.ID,
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                PROVINCES.CREATED_AT.as("provinceCreatedAt"),
                PROVINCES.UPDATED_AT.as("provinceUpdatedAt")
            )
            .from(PROVINCES)
            .where(PROVINCES.NAME.containsIgnoreCase(name))
            .fetch(record -> LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("PROVINCE")
                    .province(new ViewProvinceDTO.Output(
                            record.get(PROVINCES.ID).toString(),
                            record.get("provinceName", String.class),
                            record.get("provinceCode", String.class),
                            record.get("provinceCreatedAt", java.time.LocalDateTime.class),
                            record.get("provinceUpdatedAt", java.time.LocalDateTime.class)))
                    .hierarchyPath(record.get("provinceName", String.class))
                    .build());
    }
}
