package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.location.domains.Cell;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.CellMapper;
import minaloc.mbaza.api.location.repositories.CellRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;


@AllArgsConstructor
@Service
public class GetAllCells {

    private final CellRepository cellRepository;
    private final CellMapper cellMapper;

    public Page<CellDTO> execute(Pageable pageable) {

        try {
            Page<Cell> cellPage;
            cellPage = cellRepository.findAll(pageable);
            if (cellPage.isEmpty()) {
                throw new LocationServiceException("No cells found ");
            }

            // Map cells to DTOs
            List<CellDTO> cellDTOs = cellPage.getContent().stream()
                    .map(cellMapper::map)
                    .toList();

            return new PageImpl<>(
                    cellDTOs,
                    pageable,
                    cellPage.getTotalElements()
            );

        } catch (LocationServiceException e) {
            throw e;
        } catch (Exception ex) {
            throw new LocationServiceException(
                    "Error mapping cells ",
                    ex
            );
        }
    }
}
