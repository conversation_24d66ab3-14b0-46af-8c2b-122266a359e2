package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;

@Service
@AllArgsConstructor
public class GetSectorsByDistrictIdUseCase {

    private final DSLContext dsl;

    public PagedModel<ViewSectorDTO.Output> execute(String districtId, Pageable pageable) {
        var query = dsl.select(
                        SECTORS.ID,
                        SECTORS.NAME
                )
                .from(SECTORS)
                .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
                .where(DISTRICTS.ID.eq(districtId));

        return JooqExtensions.fetchInto(query, ViewSectorDTO.Output.class, pageable);
    }
}
