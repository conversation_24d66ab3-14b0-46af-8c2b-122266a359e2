package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewSectorDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import java.util.List;

import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;

@Service
@AllArgsConstructor
public class GetSectorsByDistrictIdUseCase {
    
    private final DSLContext dsl;
    
    public List<ViewSectorDTO.Output> execute(Long districtId) {
        return dsl.select(
                SECTORS.ID,
                SECTORS.NAME,
                SECTORS.CODE,
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                SECTORS.CREATED_AT,
                SECTORS.UPDATED_AT
            )
            .from(SECTORS)
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(DISTRICTS.ID.eq(districtId))
            .orderBy(SECTORS.NAME.asc())
            .fetchInto(ViewSectorDTO.Output.class);
    }
    
    public PagedModel<ViewSectorDTO.Output> execute(Long districtId, Pageable pageable) {
        var query = dsl.select(
                SECTORS.ID,
                SECTORS.NAME,
                SECTORS.CODE,
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                SECTORS.CREATED_AT,
                SECTORS.UPDATED_AT
            )
            .from(SECTORS)
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(DISTRICTS.ID.eq(districtId));
            
        return JooqExtensions.fetchInto(query, ViewSectorDTO.Output.class, pageable);
    }
}
