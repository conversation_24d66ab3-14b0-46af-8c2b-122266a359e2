package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import java.util.List;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;

@Service
@AllArgsConstructor
public class GetCellsBySectorIdUseCase {
    
    private final DSLContext dsl;
    
    public List<ViewCellDTO.Output> execute(Long sectorId) {
        return dsl.select(
                CELLS.ID,
                CELLS.NAME,
                CELLS.CODE,
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                CELLS.CREATED_AT,
                CELLS.UPDATED_AT
            )
            .from(CELLS)
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(SECTORS.ID.eq(sectorId))
            .orderBy(CELLS.NAME.asc())
            .fetchInto(ViewCellDTO.Output.class);
    }
    
    public PagedModel<ViewCellDTO.Output> execute(Long sectorId, Pageable pageable) {
        var query = dsl.select(
                CELLS.ID,
                CELLS.NAME,
                CELLS.CODE,
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                CELLS.CREATED_AT,
                CELLS.UPDATED_AT
            )
            .from(CELLS)
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(SECTORS.ID.eq(sectorId));
            
        return JooqExtensions.fetchInto(query, ViewCellDTO.Output.class, pageable);
    }
}
