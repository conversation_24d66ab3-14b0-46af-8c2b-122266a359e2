package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;

@Service
@AllArgsConstructor
public class GetCellsBySectorIdUseCase {

    private final DSLContext dsl;

    public PagedModel<ViewCellDTO.Output> execute(String sectorId, Pageable pageable) {
        var query = dsl.select(
                        CELLS.ID,
                        CELLS.NAME
                )
                .from(CELLS)
                .where(SECTORS.ID.eq(sectorId));

        return JooqExtensions.fetchInto(query, ViewCellDTO.Output.class, pageable);
    }
}
