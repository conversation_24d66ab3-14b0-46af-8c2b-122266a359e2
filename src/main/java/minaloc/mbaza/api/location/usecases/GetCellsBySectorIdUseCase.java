package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;

import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewCellDTO;

@Service
@AllArgsConstructor
public class GetCellsBySectorIdUseCase {

    private final DSLContext dsl;

    public PagedModel<ViewCellDTO.Output> execute(String sectorId, Pageable pageable) {
        var query = dsl.select(
                        CELLS.ID,
                        CELLS.NAME
                )
                .from(CELLS)
                .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
                .where(SECTORS.ID.eq(sectorId));

        return JooqExtensions.fetchInto(query, ViewCellDTO.Output.class, pageable);
    }
}
