package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Villages.VILLAGES;

import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;

@Service
@AllArgsConstructor
public class GetVillagesByCellIdUseCase {

    private final DSLContext dsl;

    public PagedModel<ViewVillageDTO.Output> execute(String cellId, Pageable pageable) {
        var query = dsl.select(
                        VILLAGES.ID,
                        VILLAGES.NAME
                )
                .from(VILLAGES)
                .join(CELLS).on(CELLS.ID.eq(VILLAGES.CELL_ID))
                .where(CELLS.ID.eq(cellId));

        return JooqExtensions.fetchInto(query, ViewVillageDTO.Output.class, pageable);
    }
}
