package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;

import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewDistrictDTO;

@Service
@AllArgsConstructor
public class GetDistrictsByProvinceNameJooqUseCase {
    
    private final DSLContext dsl;
    
    public PagedModel<ViewDistrictDTO.Output> execute(String provinceName, Pageable pageable) {
        var query = dsl.select(
                DISTRICTS.ID,
                DISTRICTS.NAME,
                DISTRICTS.CODE,
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                DISTRICTS.CREATED_AT,
                DISTRICTS.UPDATED_AT
            )
            .from(DISTRICTS)
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(PROVINCES.NAME.equalIgnoreCase(provinceName));
            
        return JooqExtensions.fetchInto(query, ViewDistrictDTO.Output.class, pageable);
    }
}
