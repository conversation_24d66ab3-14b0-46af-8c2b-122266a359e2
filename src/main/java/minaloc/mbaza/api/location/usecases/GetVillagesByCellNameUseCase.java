package minaloc.mbaza.api.location.usecases;


import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Village;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.VillageMapper;
import minaloc.mbaza.api.location.repositories.VillageRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class GetVillagesByCellNameUseCase {

    private final VillageRepository villageRepository;
    private final VillageMapper villageMapper;


    public List<VillageDTO> execute(String cellName) {
        List<Village> villages = villageRepository.findByCell_NameIgnoreCase(cellName);

        try {
            return villages.stream()
                    .map(villageMapper::map)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new LocationServiceException("Failed to map villages for cell: " + cellName, e);
        }
    }

    public Page<VillageDTO> execute(String cellName, Pageable pageable) {
        Page<Village> villagePage = villageRepository.findByCell_NameIgnoreCase(cellName, pageable);
        if (villagePage.isEmpty()) {
            throw new LocationServiceException("No villages found for cell name: " + cellName);
        }

        try {
            List<VillageDTO> villageDTOs = villagePage.getContent().stream()
                    .map(villageMapper::map)
                    .toList();
            return new PageImpl<>(villageDTOs, pageable, villagePage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Failed to map paginated villages for cell: " + cellName, e);
        }
    }

}