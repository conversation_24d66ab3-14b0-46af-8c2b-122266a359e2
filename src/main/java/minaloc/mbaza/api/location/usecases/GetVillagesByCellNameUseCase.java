package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.common.extensions.JooqExtensions;
import minaloc.mbaza.api.location.dtos.ViewVillageDTO;
import org.jooq.DSLContext;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;

import java.util.List;

import static minaloc.mbaza.jooq.generated.tables.Cells.CELLS;
import static minaloc.mbaza.jooq.generated.tables.Districts.DISTRICTS;
import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;
import static minaloc.mbaza.jooq.generated.tables.Sectors.SECTORS;
import static minaloc.mbaza.jooq.generated.tables.Villages.VILLAGES;

@Service
@AllArgsConstructor
public class GetVillagesByCellNameUseCase {
    
    private final DSLContext dsl;
    
    public List<ViewVillageDTO.Output> execute(String cellName) {
        return dsl.select(
                VILLAGES.ID,
                VILLAGES.NAME,
                VILLAGES.CODE,
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                VILLAGES.CREATED_AT,
                VILLAGES.UPDATED_AT
            )
            .from(VILLAGES)
            .join(CELLS).on(VILLAGES.CELL_ID.eq(CELLS.ID))
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(CELLS.NAME.equalIgnoreCase(cellName))
            .orderBy(VILLAGES.NAME.asc())
            .fetchInto(ViewVillageDTO.Output.class);
    }
    
    public PagedModel<ViewVillageDTO.Output> execute(String cellName, Pageable pageable) {
        var query = dsl.select(
                VILLAGES.ID,
                VILLAGES.NAME,
                VILLAGES.CODE,
                CELLS.NAME.as("cellName"),
                CELLS.CODE.as("cellCode"),
                SECTORS.NAME.as("sectorName"),
                SECTORS.CODE.as("sectorCode"),
                DISTRICTS.NAME.as("districtName"),
                DISTRICTS.CODE.as("districtCode"),
                PROVINCES.NAME.as("provinceName"),
                PROVINCES.CODE.as("provinceCode"),
                VILLAGES.CREATED_AT,
                VILLAGES.UPDATED_AT
            )
            .from(VILLAGES)
            .join(CELLS).on(VILLAGES.CELL_ID.eq(CELLS.ID))
            .join(SECTORS).on(CELLS.SECTOR_ID.eq(SECTORS.ID))
            .join(DISTRICTS).on(SECTORS.DISTRICT_ID.eq(DISTRICTS.ID))
            .join(PROVINCES).on(DISTRICTS.PROVINCE_ID.eq(PROVINCES.ID))
            .where(CELLS.NAME.equalIgnoreCase(cellName));
            
        return JooqExtensions.fetchInto(query, ViewVillageDTO.Output.class, pageable);
    }
}
