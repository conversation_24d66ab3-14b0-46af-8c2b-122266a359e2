package minaloc.mbaza.api.location.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Cell;
import minaloc.mbaza.api.location.dtos.CellDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.CellMapper;
import minaloc.mbaza.api.location.repositories.CellRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GetCellsBySectorNameUseCase {

    private final CellRepository cellRepository;
    private final CellMapper cellMapper;

    public Page<CellDTO> execute(String sectorName, Pageable pageable) {

        try {
            Page<Cell> cellPage;
            cellPage = cellRepository.findBySector_NameIgnoreCase(sectorName, pageable);


            // Map cells to DTOs
            List<CellDTO> cellDTOs = cellPage.getContent().stream()
                    .map(cellMapper::map)
                    .toList();

            return new PageImpl<>(
                    cellDTOs,
                    pageable,
                    cellPage.getTotalElements()
            );

        } catch (LocationServiceException e) {
            throw e;
        } catch (Exception ex) {
            throw new LocationServiceException(
                    "Error mapping cells for sector: " + sectorName + " - " + ex.getMessage(),
                    ex
            );
        }
    }
    // with out paginations

    public List<CellDTO> execute(String sectorName) {
        List<Cell> cells = cellRepository.findBySector_NameIgnoreCase(sectorName);
        if (cells.isEmpty()) {
            throw new LocationServiceException("No cells found for sector name: " + sectorName);
        }
        try {
            return cells.stream()
                    .map(cellMapper::map)
                    .toList();
        } catch (Exception ex) {
            throw new LocationServiceException("Error mapping cells for sector: " + sectorName + " - " + ex.getMessage(), ex);
        }
    }
}
