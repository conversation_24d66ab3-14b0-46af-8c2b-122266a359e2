package minaloc.mbaza.api.location.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Province;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.ProvinceMapper;
import minaloc.mbaza.api.location.repositories.ProvinceRepository;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class GetAllProvincesUseCase {

    private final ProvinceRepository provinceRepository;
    private final ProvinceMapper provinceMapper;

    public List<ProvinceDTO> execute() {
        try {
            List<Province> provinces = provinceRepository.findAll();
            if (provinces.isEmpty()) {
                throw new LocationServiceException("No provinces found in the database.");
            }
            return provinces.stream()
                    .map(provinceMapper::map)
                    .toList();
        } catch (Exception e) {
            throw new LocationServiceException("An error occurred while fetching provinces.", e);
        }
    }
}