package minaloc.mbaza.api.location.usecases;

import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;

import java.util.List;

import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;

@Service
@AllArgsConstructor
public class GetAllProvincesUseCase {
    
    private final DSLContext dsl;
    
    public List<ViewProvinceDTO.Output> execute() {
        return dsl.selectFrom(PROVINCES)
                .orderBy(PROVINCES.NAME.asc())
                .fetchInto(ViewProvinceDTO.Output.class);
    }
}
