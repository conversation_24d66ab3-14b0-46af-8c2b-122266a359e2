package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.location.dtos.ViewProvinceDTO;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.util.List;

import static minaloc.mbaza.jooq.generated.tables.Provinces.PROVINCES;

@Service
@AllArgsConstructor
public class GetAllProvincesUseCase {

    private final DSLContext dsl;

    public List<ViewProvinceDTO.Output> execute() {
        return dsl.select(
                        PROVINCES.ID,
                        PROVINCES.NAME
                )
                .from(PROVINCES)
                .orderBy(PROVINCES.NAME.asc())
                .fetchInto(ViewProvinceDTO.Output.class);
    }
}
