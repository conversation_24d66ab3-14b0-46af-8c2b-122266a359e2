package minaloc.mbaza.api.location.mappers;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.District;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistrictMapper {

    private final SectorMapper sectorMapper;

    public DistrictDTO map(District district) {
        return new DistrictDTO(district.getName(), district.getCode());
    }

}