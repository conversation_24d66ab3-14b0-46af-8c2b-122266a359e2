package minaloc.mbaza.api.location.seeds;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import minaloc.mbaza.api.common.exceptions.BadRequestException;
import minaloc.mbaza.api.location.domains.*;
import minaloc.mbaza.api.location.repositories.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class LocationSeed {

    private final ProvinceRepository provinceRepository;
    private final DistrictRepository districtRepository;
    private final SectorRepository sectorRepository;
    private final CellRepository cellRepository;
    private final VillageRepository villageRepository;
    private final ObjectMapper objectMapper;

    @Transactional
    public void seedLocations() {
        if (isAlreadySeeded()) {
            log.info("Locations already seeded. Skipping...");
            return;
        }

        try {
            List<LocationDataFormat.ProvinceDto> provinces = loadLocationData();
            processLocationData(provinces);
            log.info("Location seeding completed successfully.");
        } catch (Exception e) {
            log.error("Failed to seed locations", e);
            throw new BadRequestException(e.getMessage());
        }
    }

    private boolean isAlreadySeeded() {
        return provinceRepository.count() > 0;
    }

    private List<LocationDataFormat.ProvinceDto> loadLocationData() throws Exception {
        try (InputStream inputStream = new ClassPathResource("seed/location/locations.json").getInputStream()) {
            return objectMapper.readValue(inputStream,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, LocationDataFormat.ProvinceDto.class));
        }
    }

    private void processLocationData(List<LocationDataFormat.ProvinceDto> provinces) {
        provinces.forEach(this::processProvince);
    }

    private void processProvince(LocationDataFormat.ProvinceDto provinceDto) {
        Province province = createAndSaveProvince(provinceDto);
        log.info("Created province: {}", province.getName());

        provinceDto.getDistricts().forEach(districtDto -> processDistrict(districtDto, province));
    }

    private Province createAndSaveProvince(LocationDataFormat.ProvinceDto provinceDto) {
        Province province = Province.builder()
                .name(normalizeLocationName(provinceDto.getName()))
                .code(provinceDto.getCode())
                .build();
        return provinceRepository.save(province);
    }

    private void processDistrict(LocationDataFormat.DistrictDto districtDto, Province province) {
        District district = createAndSaveDistrict(districtDto, province);
        log.info("Created district: {}", district.getName());

        districtDto.getSectors().forEach(sectorDto -> processSector(sectorDto, district));
    }

    private District createAndSaveDistrict(LocationDataFormat.DistrictDto districtDto, Province province) {
        District district = District.builder()
                .name(normalizeLocationName(districtDto.getName()))
                .code(districtDto.getCode())
                .province(province)
                .build();
        return districtRepository.save(district);
    }

    private void processSector(LocationDataFormat.SectorDto sectorDto, District district) {
        Sector sector = createAndSaveSector(sectorDto, district);
        log.info("Created sector: {}", sector.getName());

        sectorDto.getCells().forEach(cellDto -> processCell(cellDto, sector));
    }

    private Sector createAndSaveSector(LocationDataFormat.SectorDto sectorDto, District district) {
        Sector sector = Sector.builder()
                .name(normalizeLocationName(sectorDto.getName()))
                .code(sectorDto.getCode())
                .district(district)
                .build();
        return sectorRepository.save(sector);
    }

    private void processCell(LocationDataFormat.CellDto cellDto, Sector sector) {
        Cell cell = createAndSaveCell(cellDto, sector);

        List<Village> villages = cellDto.getVillages().stream()
                .map(villageDto -> createVillage(villageDto, cell))
                .toList();

        villageRepository.saveAll(villages);
        log.info("Seeded cell {} with {} villages", cell.getName(), villages.size());
    }

    private Cell createAndSaveCell(LocationDataFormat.CellDto cellDto, Sector sector) {
        Cell cell = Cell.builder()
                .name(normalizeLocationName(cellDto.getName()))
                .code(cellDto.getCode())
                .sector(sector)
                .build();
        return cellRepository.save(cell);
    }

    private Village createVillage(LocationDataFormat.VillageDto villageDto, Cell cell) {
        return Village.builder()
                .name(normalizeLocationName(villageDto.getName()))
                .code(villageDto.getCode())
                .cell(cell)
                .build();
    }

    private String normalizeLocationName(String name) {
        return name != null ? name.trim().toUpperCase() : null;
    }
}