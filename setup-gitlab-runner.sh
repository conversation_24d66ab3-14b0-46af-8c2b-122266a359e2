#!/bin/bash

# GitLab Runner Setup Script
# This script helps you set up a local GitLab Runner for testing CI/CD pipelines

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
print_header "CHECKING PREREQUISITES"

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    echo "Please start Docker and try again"
    exit 1
fi

print_success "Docker is installed and running"

# Get GitLab information
print_header "GITLAB CONFIGURATION"

echo "Please provide your GitLab information:"
echo ""

read -p "GitLab URL (default: https://gitlab.com/): " GITLAB_URL
GITLAB_URL=${GITLAB_URL:-https://gitlab.com/}

read -p "Registration Token (from Project Settings > CI/CD > Runners): " REGISTRATION_TOKEN

if [ -z "$REGISTRATION_TOKEN" ]; then
    print_error "Registration token is required"
    echo "Get it from: Project Settings > CI/CD > Runners section"
    exit 1
fi

read -p "Runner Description (default: Local Docker Runner): " RUNNER_DESCRIPTION
RUNNER_DESCRIPTION=${RUNNER_DESCRIPTION:-"Local Docker Runner"}

read -p "Runner Tags (default: local,docker): " RUNNER_TAGS
RUNNER_TAGS=${RUNNER_TAGS:-"local,docker"}

# Setup GitLab Runner
print_header "SETTING UP GITLAB RUNNER"

echo "Creating GitLab Runner configuration volume..."
docker volume create gitlab-runner-config 2>/dev/null || true
print_success "Configuration volume created"

echo "Stopping existing GitLab Runner (if any)..."
docker stop gitlab-runner 2>/dev/null || true
docker rm gitlab-runner 2>/dev/null || true

echo "Starting GitLab Runner container..."
docker run -d --name gitlab-runner --restart always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v gitlab-runner-config:/etc/gitlab-runner \
  gitlab/gitlab-runner:latest

print_success "GitLab Runner container started"

# Wait for container to be ready
echo "Waiting for GitLab Runner to be ready..."
sleep 5

# Register the runner
print_header "REGISTERING RUNNER"

echo "Registering runner with GitLab..."
docker run --rm -it -v gitlab-runner-config:/etc/gitlab-runner \
  gitlab/gitlab-runner:latest register \
  --non-interactive \
  --url "$GITLAB_URL" \
  --registration-token "$REGISTRATION_TOKEN" \
  --executor "docker" \
  --docker-image "alpine:latest" \
  --description "$RUNNER_DESCRIPTION" \
  --tag-list "$RUNNER_TAGS" \
  --docker-privileged \
  --docker-volumes "/var/run/docker.sock:/var/run/docker.sock"

print_success "Runner registered successfully"

# Verify registration
print_header "VERIFICATION"

echo "Checking runner status..."
docker exec gitlab-runner gitlab-runner list

echo ""
echo "Testing Docker access from runner..."
if docker exec gitlab-runner docker info > /dev/null 2>&1; then
    print_success "Docker access from runner: OK"
else
    print_error "Docker access from runner: FAILED"
fi

echo ""
echo "Testing Testcontainers image pull..."
if docker exec gitlab-runner docker pull postgres:16.4-alpine3.20 > /dev/null 2>&1; then
    print_success "Testcontainers image pull: OK"
else
    print_warning "Testcontainers image pull: FAILED (may work during actual pipeline)"
fi

# Final instructions
print_header "SETUP COMPLETE"

print_success "GitLab Runner is now set up and ready!"
echo ""
echo "📋 Next steps:"
echo "1. Add tags to your .gitlab-ci.yml jobs:"
echo "   generate-jooq:"
echo "     tags:"
echo "       - local"
echo ""
echo "2. Push your code to trigger the pipeline"
echo ""
echo "3. Monitor the pipeline in GitLab UI or check runner logs:"
echo "   docker logs -f gitlab-runner"
echo ""
echo "🔧 Useful commands:"
echo "   Start runner:  docker start gitlab-runner"
echo "   Stop runner:   docker stop gitlab-runner"
echo "   View logs:     docker logs gitlab-runner"
echo "   Runner status: docker exec gitlab-runner gitlab-runner list"
echo ""
echo "📖 For more details, see: GITLAB_RUNNER_SETUP.md"
