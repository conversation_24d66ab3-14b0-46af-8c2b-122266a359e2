# Complete Testing Guide for CI/CD Pipeline

This guide provides multiple ways to test your GitLab CI/CD pipeline locally before deploying to production.

## 🎯 **Testing Options Overview**

| Method | Complexity | Accuracy | Use Case |
|--------|------------|----------|----------|
| **Local Scripts** | Low | Medium | Quick validation |
| **Real GitLab Runner** | Medium | High | Full CI simulation |
| **Environment Testing** | Low | High | App functionality |

## 🚀 **Option 1: Quick Local Testing**

### Basic Pipeline Test
```bash
# Test the complete pipeline locally
./test-ci-locally.sh
```

### With Environment Variables
```bash
# Test with your .env file
./test-with-env.sh
```

### Individual Components
```bash
# Test only JOOQ generation
./generate-jooq.sh

# Test only Docker build
./mvnw spring-boot:build-image -Dspring-boot.build-image.imageName=test:latest -DskipTests

# Test with environment file
docker run -p 8080:8080 --env-file .env test:latest
```

## 🏗️ **Option 2: Real GitLab Runner (Recommended)**

### Setup GitLab Runner
```bash
# Quick setup with guided prompts
./setup-gitlab-runner.sh
```

### Manual Setup
1. **Install GitLab Runner**:
   ```bash
   # Docker-based (recommended)
   docker volume create gitlab-runner-config
   docker run -d --name gitlab-runner --restart always \
     -v /var/run/docker.sock:/var/run/docker.sock \
     -v gitlab-runner-config:/etc/gitlab-runner \
     gitlab/gitlab-runner:latest
   ```

2. **Register Runner**:
   ```bash
   docker run --rm -it -v gitlab-runner-config:/etc/gitlab-runner \
     gitlab/gitlab-runner:latest register \
     --non-interactive \
     --url "https://gitlab.com/" \
     --registration-token "YOUR_TOKEN" \
     --executor "docker" \
     --docker-image alpine:latest \
     --description "Local Docker Runner" \
     --tag-list "local,docker" \
     --docker-privileged \
     --docker-volumes "/var/run/docker.sock:/var/run/docker.sock"
   ```

3. **Update .gitlab-ci.yml**:
   ```yaml
   generate-jooq:
     tags:
       - local  # Add this to run on your local runner
   ```

4. **Push and Test**:
   ```bash
   git add .
   git commit -m "Test with local runner"
   git push origin your-branch
   ```

## 🔧 **Environment File Integration**

### Create Test Environment
```bash
# Copy your .env file for testing
cp .env .env.test

# Edit test values
nano .env.test
```

### Test with Environment
```bash
# Test application with environment file
docker run -p 8080:8080 --env-file .env.test your-image:latest

# Check health endpoint
curl http://localhost:8080/actuator/health
```

## 📊 **Monitoring and Debugging**

### GitLab Runner Logs
```bash
# View runner logs
docker logs -f gitlab-runner

# Check runner status
docker exec gitlab-runner gitlab-runner list

# Test Docker access
docker exec gitlab-runner docker info
```

### Application Logs
```bash
# View container logs
docker logs your-container-id

# Follow logs in real-time
docker logs -f your-container-id

# Get last 50 lines
docker logs --tail 50 your-container-id
```

### Pipeline Debugging
```bash
# Test specific job locally
docker exec gitlab-runner gitlab-runner exec docker generate-jooq

# Debug with verbose output
docker exec gitlab-runner gitlab-runner --debug exec docker generate-jooq
```

## ✅ **Validation Checklist**

### Before Pushing to GitLab:
- [ ] JOOQ generation works locally (`./generate-jooq.sh`)
- [ ] Docker image builds successfully
- [ ] Application starts with environment file
- [ ] Health endpoints respond correctly
- [ ] Database connectivity works (if applicable)

### GitLab Runner Setup:
- [ ] Runner is registered and active
- [ ] Docker-in-Docker is working
- [ ] Testcontainers can pull images
- [ ] Pipeline jobs have correct tags

### Environment Configuration:
- [ ] .env file contains all required variables
- [ ] Database credentials are correct
- [ ] JWT secrets are set
- [ ] Application-specific configs are present

## 🐛 **Common Issues and Solutions**

### JOOQ Generation Fails
```bash
# Check Docker daemon
docker info

# Test PostgreSQL image pull
docker pull postgres:16.4-alpine3.20

# Check Flyway migrations
ls -la src/main/resources/db/migration/
```

### Docker Build Fails
```bash
# Check if JOOQ code exists
ls -la target/generated-sources/jooq/

# Build without tests
./mvnw spring-boot:build-image -DskipTests

# Check Maven dependencies
./mvnw dependency:resolve
```

### Application Won't Start
```bash
# Check environment variables
docker run --env-file .env your-image env

# Check application logs
docker logs your-container

# Test with minimal config
docker run -e SPRING_PROFILES_ACTIVE=test your-image
```

### GitLab Runner Issues
```bash
# Restart runner
docker restart gitlab-runner

# Re-register runner
docker exec gitlab-runner gitlab-runner unregister --all-runners
# Then re-register

# Check configuration
docker exec gitlab-runner cat /etc/gitlab-runner/config.toml
```

## 📈 **Performance Optimization**

### Local Testing
```bash
# Use Maven offline mode
./mvnw -o spring-boot:build-image

# Cache Docker layers
export DOCKER_BUILDKIT=1

# Parallel Maven builds
./mvnw -T 1C spring-boot:build-image
```

### GitLab Runner
```toml
# In /etc/gitlab-runner/config.toml
[[runners]]
  [runners.docker]
    volumes = ["/cache", "/var/run/docker.sock:/var/run/docker.sock"]
    disable_cache = false
    shm_size = 268435456  # 256MB
```

## 🔐 **Security Best Practices**

### Local Testing
- Use test credentials in .env.test
- Don't commit sensitive data
- Use separate database for testing
- Rotate test tokens regularly

### GitLab Runner
- Use dedicated runner for testing
- Limit runner access to test projects
- Regular security updates
- Monitor runner logs for suspicious activity

## 📚 **Additional Resources**

- **GITLAB_RUNNER_SETUP.md**: Detailed runner setup guide
- **CI_CD_SETUP.md**: Complete CI/CD pipeline documentation
- **JOOQ_GENERATION.md**: JOOQ generation specifics
- **GitLab CI/CD Docs**: https://docs.gitlab.com/ee/ci/

## 🎯 **Next Steps**

1. **Choose your testing method** based on your needs
2. **Run local tests** to validate functionality
3. **Set up GitLab Runner** for full CI simulation
4. **Test with environment files** to ensure proper configuration
5. **Push to GitLab** and monitor the pipeline
6. **Deploy to production** once everything is validated
