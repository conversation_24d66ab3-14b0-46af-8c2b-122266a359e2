# JOOQ Code Generation with Testcontainers

This project uses JOOQ for type-safe SQL queries and Testcontainers for code generation to avoid hardcoded database connections in the build process.

## Overview

The JOOQ code generation process:

1. Starts a PostgreSQL container using Testcontainers
2. Applies all Flyway migrations to create the latest schema
3. Generates JOOQ code from the migrated schema
4. Cleans up the container automatically

## Prerequisites

- Docker must be installed and running on your machine
- Java 24 (as specified in the project)
- Maven 3.6+

## How to Generate JOOQ Code

### Option 1: Using the provided script (Recommended)

```bash
./generate-jooq.sh
```

### Option 2: Using <PERSON>ven directly

```bash
# Step 1: Compile test classes (skip main compilation to avoid dependency issues)
./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q

# Step 2: Run the JOOQ code generator
./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q

# Step 3: Compile the main project with generated code
./mvnw compile
```

### Option 3: Using your IDE

If your IDE supports Maven profiles, you can run the `jooq-testcontainers` profile directly from your IDE.

## Generated Code Location

The generated JOOQ code will be placed in:

```
target/generated-sources/jooq/
```

Your IDE should automatically recognize this as a source folder. If not, you may need to manually mark it as a source directory.

## Configuration

The JOOQ generation configuration is defined in:

- `src/test/java/minaloc/mbaza/api/codegen/TestcontainersJooqCodegen.java`

Key configuration options:

- **Package Name**: `minaloc.mbaza.jooq.generated`
- **Database Schema**: `public`
- **Excluded Tables**: `flyway.*` (Flyway metadata tables)
- **Generated Features**: Records, Immutable POJOs, Fluent Setters, Instance Fields

## Customization

To modify the JOOQ generation settings:

1. Edit `TestcontainersJooqCodegen.java`
2. Modify the `Configuration` object in the `generateJooqCode` method
3. Re-run the generation process

## Troubleshooting

### Docker Issues

- Ensure Docker is running
- Check that you have sufficient disk space
- Verify Docker has permission to pull images

### Generation Fails

- Check that all Flyway migrations are valid
- Ensure there are no syntax errors in your SQL files
- Verify the PostgreSQL image is accessible

### IDE Not Recognizing Generated Code

- Refresh/reimport your Maven project
- Manually mark `target/generated-sources/jooq` as a source folder
- Clean and rebuild your project

## Benefits of This Approach

1. **No Hardcoded Connections**: No database credentials in build files
2. **Always Up-to-Date**: Generated code reflects the latest migrations
3. **Isolated Environment**: Each generation uses a fresh database
4. **CI/CD Friendly**: Works in any environment with Docker
5. **No External Dependencies**: No need for a running database during build

## Migration from Old Setup

The old JOOQ plugin configuration has been commented out in `pom.xml`. If you need to revert:

1. Uncomment the `jooq-codegen-maven` plugin in `pom.xml`
2. Remove or comment out the `jooq-testcontainers` profile
3. Ensure your database is running with the correct schema

## Integration with CI/CD

This setup works well in CI/CD environments:

```yaml
# Example GitHub Actions step
- name: Generate JOOQ Code
  run: ./generate-jooq.sh
```

The Testcontainers will automatically handle the database lifecycle during the build process.
