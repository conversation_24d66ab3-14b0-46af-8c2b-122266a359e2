stages:
  - build
  - deploy

variables:
  DOCKER_USERNAME: gitlab-ci-token
  DOCKER_PASSWORD: $CI_JOB_TOKEN
  BP_JVM_VERSION: 24

.build-docker-image-template: &build-docker-image-template
  stage: build
  script:
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    - mvn clean spring-boot:build-image -Dspring-boot.build-image.imageName=$BASE_TAG_NAME:latest -Dspring-boot.build-image.publish=true

.deploy-template: &deploy-template
  stage: deploy
  script:
    - "which ssh-agent || (  apk update  && apk add openssh-client )"
    - "which rsync || ( apk update  && apk add rsync  )"
    - eval $(ssh-agent -s)
    # Inject the remote's private key
    - echo "$PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    # Append keyscan output into known hosts
    - ssh-keyscan $PUBLIC_IP_ADDRESS >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - echo "Deploying to $DEPLOY_ENVIRONMENT"
    - which envsubst || apk add gettext
    - envsubst '${BASE_TAG_NAME}' < /$CI_PROJECT_DIR/compose/$COMPOSE_FILE_NAME > compose.yml
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" compose.yml $USER@$PUBLIC_IP_ADDRESS:$SERVER_PATH
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" $ENV_FILE $USER@$PUBLIC_IP_ADDRESS:$SERVER_PATH/.env
    - echo "STARTING DOCKER IMAGE"
    - ssh $USER@$PUBLIC_IP_ADDRESS "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY &&
      cd $SERVER_PATH &&
      docker image rm -f $BASE_TAG_NAME:latest &&
      docker pull $BASE_TAG_NAME:latest &&
      docker compose -f $SERVER_PATH/compose.yml -p  $PROJECT_NAME down &&
      docker compose -f $SERVER_PATH/compose.yml -p  $PROJECT_NAME up -d"

build-dev-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  only:
    - dev

#This job will deploy our application to the development environment and run in a docker container
deploy-to-dev-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $DEV_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $DEV_PUBLIC_IP_ADDRESS
    USER: $DEV_USER
    SERVER_PATH: $DEV_SERVER_PATH
    COMPOSE_FILE_NAME: dev.compose.yml
    ENV_FILE: $dev_env
    DEPLOY_ENVIRONMENT: development
    PROJECT_NAME: $DEV_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  environment:
    name: development
  only:
    - dev

build-staging-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/staging
  only:
    - /^release\//
    - /^hotfix\//

#This job will deploy our application to the staging environment and run in a docker container
deploy-to-staging-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $STG_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $STG_PUBLIC_IP_ADDRESS
    USER: $STG_USER
    SERVER_PATH: $STG_SERVER_PATH
    COMPOSE_FILE_NAME: stg.compose.yml
    ENV_FILE: $stg_env
    DEPLOY_ENVIRONMENT: staging
    PROJECT_NAME: $STG_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/staging
  environment:
    name: staging
  only:
    - /^release\//
    - /^hotfix\//

build-prod-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  only:
    - main

#This job will deploy our application to the production environment and run in a docker container
deploy-to-prod-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $PROD_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $PROD_PUBLIC_IP_ADDRESS
    USER: $PROD_USER
    SERVER_PATH: $PROD_SERVER_PATH
    COMPOSE_FILE_NAME: prod.compose.yml
    ENV_FILE: $prod_env
    DEPLOY_ENVIRONMENT: production
    PROJECT_NAME: $PROD_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  environment:
    name: production
  only:
    - main