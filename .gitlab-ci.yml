# GitLab CI/CD Pipeline for Spring Boot API with JOOQ Code Generation
#
# Pipeline stages:
# 1. generate: Generate JOOQ code using Testcontainers and Flyway migrations
# 2. build: Build Docker image with generated JOOQ code
# 3. deploy: Deploy to target environment
#
# Key features:
# - Uses Testcontainers for isolated JOOQ code generation
# - No hardcoded database connections required
# - Automatic Flyway migration application
# - Caching for Maven dependencies and generated code
# - Supports dev, staging, and production environments

stages:
  - generate
  - build
  - deploy

variables:
  DOCKER_USERNAME: gitlab-ci-token
  DOCKER_PASSWORD: $CI_JOB_TOKEN
  BP_JVM_VERSION: 24
  # Docker-in-Docker service for Testcontainers
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"

# Cache configuration for Maven dependencies
cache:
  paths:
    - .m2/repository/
    - target/

# JOOQ code generation job
generate-jooq:
  stage: generate
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  timeout: 15m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  before_script:
    # Install Java 24 and Maven
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    # Configure Maven to use local cache
    - export MAVEN_OPTS="-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
    # Wait for Docker daemon to be ready
    - until docker info; do sleep 1; done
  script:
    - echo "🔧 Generating JOOQ code using Testcontainers..."
    - ./mvnw clean process-test-resources compiler:testCompile -Dmaven.main.skip=true -q
    - ./mvnw exec:java -Dexec.mainClass="minaloc.mbaza.api.codegen.TestcontainersJooqCodegen" -Dexec.classpathScope="test" -q || echo "JOOQ generation completed (ignoring Maven interruption)"
    # Verify JOOQ code was generated
    - |
      if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
        echo "✅ JOOQ code generated successfully"
        echo "Generated files:"
        ls -la target/generated-sources/jooq/minaloc/mbaza/jooq/generated/
      else
        echo "❌ JOOQ code generation failed"
        exit 1
      fi
  artifacts:
    paths:
      - target/generated-sources/jooq/
    expire_in: 1 hour
  only:
    - dev
    - /^release\//
    - /^hotfix\//
    - main

.build-docker-image-template: &build-docker-image-template
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  dependencies:
    - generate-jooq
  before_script:
    # Install Java 24 and Maven
    - apk add openjdk24-jdk maven --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing
    # Configure Maven to use local cache
    - export MAVEN_OPTS="-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
    # Wait for Docker daemon to be ready
    - until docker info; do sleep 1; done
  script:
    # Verify JOOQ code exists from previous stage
    - |
      if [ -d "target/generated-sources/jooq/minaloc/mbaza/jooq/generated" ] && [ "$(ls -A target/generated-sources/jooq/minaloc/mbaza/jooq/generated)" ]; then
        echo "✅ Using JOOQ code from previous stage"
      else
        echo "❌ JOOQ code not found from previous stage"
        exit 1
      fi
    # Build Docker image with generated code
    - echo "🐳 Building Docker image..."
    - ./mvnw clean spring-boot:build-image -Dspring-boot.build-image.imageName=$BASE_TAG_NAME:latest -Dspring-boot.build-image.publish=true

.deploy-template: &deploy-template
  stage: deploy
  script:
    - "which ssh-agent || (  apk update  && apk add openssh-client )"
    - "which rsync || ( apk update  && apk add rsync  )"
    - eval $(ssh-agent -s)
    # Inject the remote's private key
    - echo "$PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    # Append keyscan output into known hosts
    - ssh-keyscan $PUBLIC_IP_ADDRESS >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - echo "Deploying to $DEPLOY_ENVIRONMENT"
    - which envsubst || apk add gettext
    - envsubst '${BASE_TAG_NAME}' < /$CI_PROJECT_DIR/compose/$COMPOSE_FILE_NAME > compose.yml
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" compose.yml $USER@$PUBLIC_IP_ADDRESS:$SERVER_PATH
    - rsync --rsync-path=/usr/bin/rsync --delete -avuz --exclude=".*" $ENV_FILE $USER@$PUBLIC_IP_ADDRESS:$SERVER_PATH/.env
    - echo "STARTING DOCKER IMAGE"
    - ssh $USER@$PUBLIC_IP_ADDRESS "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY &&
      cd $SERVER_PATH &&
      docker image rm -f $BASE_TAG_NAME:latest &&
      docker pull $BASE_TAG_NAME:latest &&
      docker compose -f $SERVER_PATH/compose.yml -p  $PROJECT_NAME down &&
      docker compose -f $SERVER_PATH/compose.yml -p  $PROJECT_NAME up -d"

build-dev-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  only:
    - dev

#This job will deploy our application to the development environment and run in a docker container
deploy-to-dev-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $DEV_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $DEV_PUBLIC_IP_ADDRESS
    USER: $DEV_USER
    SERVER_PATH: $DEV_SERVER_PATH
    COMPOSE_FILE_NAME: dev.compose.yml
    ENV_FILE: $dev_env
    DEPLOY_ENVIRONMENT: development
    PROJECT_NAME: $DEV_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  environment:
    name: development
  only:
    - dev

build-staging-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/staging
  only:
    - /^release\//
    - /^hotfix\//

#This job will deploy our application to the staging environment and run in a docker container
deploy-to-staging-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $STG_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $STG_PUBLIC_IP_ADDRESS
    USER: $STG_USER
    SERVER_PATH: $STG_SERVER_PATH
    COMPOSE_FILE_NAME: stg.compose.yml
    ENV_FILE: $stg_env
    DEPLOY_ENVIRONMENT: staging
    PROJECT_NAME: $STG_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/staging
  environment:
    name: staging
  only:
    - /^release\//
    - /^hotfix\//

build-prod-docker-image:
  <<: *build-docker-image-template
  variables:
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  only:
    - main

#This job will deploy our application to the production environment and run in a docker container
deploy-to-prod-environment:
  <<: *deploy-template
  variables:
    PRIVATE_KEY: $PROD_PRIVATE_KEY
    PUBLIC_IP_ADDRESS: $PROD_PUBLIC_IP_ADDRESS
    USER: $PROD_USER
    SERVER_PATH: $PROD_SERVER_PATH
    COMPOSE_FILE_NAME: prod.compose.yml
    ENV_FILE: $prod_env
    DEPLOY_ENVIRONMENT: production
    PROJECT_NAME: $PROD_PROJECT_NAME
    BASE_TAG_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG
  environment:
    name: production
  only:
    - main
