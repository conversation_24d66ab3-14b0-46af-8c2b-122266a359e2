package minaloc.mbaza.api.location.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Village;
import minaloc.mbaza.api.location.dtos.VillageDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.VillageMapper;
import minaloc.mbaza.api.location.repositories.VillageRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GetAllVillages {


    private final VillageRepository villageRepository;
    private final VillageMapper villageMapper;

    public Page<VillageDTO> execute(Pageable pageable) {
        Page<Village> villagePage = villageRepository.findAll(pageable);
        if (villagePage.isEmpty()) {
            throw new LocationServiceException("No villages found");
        }

        try {
            List<VillageDTO> villageDTOs = villagePage.getContent().stream()
                    .map(villageMapper::map)
                    .toList();
            return new PageImpl<>(villageDTOs, pageable, villagePage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Failed to map paginated villages ", e);
        }
    }
}
