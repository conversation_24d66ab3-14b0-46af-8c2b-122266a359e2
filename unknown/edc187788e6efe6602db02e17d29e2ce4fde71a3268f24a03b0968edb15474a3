package minaloc.mbaza.api.location.mappers;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Province;
import minaloc.mbaza.api.location.dtos.ProvinceDTO;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProvinceMapper {

    private final DistrictMapper districtMapper;

    public ProvinceDTO map(Province province) {
        return new ProvinceDTO(province.getName(), province.getCode());
    }


}