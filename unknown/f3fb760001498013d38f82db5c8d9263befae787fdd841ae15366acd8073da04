package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.location.domains.Sector;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.SectorMapper;
import minaloc.mbaza.api.location.repositories.SectorRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;


@AllArgsConstructor
@Service
public class GetAllSectors {
    private final SectorRepository sectorRepository;
    private final SectorMapper sectorMapper;

    public Page<SectorDTO> execute(Pageable pageable) {
        Page<Sector> sectorPage = sectorRepository.findAll(pageable);
        if (sectorPage.isEmpty()) {
            throw new LocationServiceException("No sectors found for district name: ");
        }

        try {
            List<SectorDTO> sectorDTOs = sectorPage.getContent().stream()
                    .map(sectorMapper::map)
                    .toList();
            return new PageImpl<>(sectorDTOs, pageable, sectorPage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping paginated sectors");
        }
    }
}
