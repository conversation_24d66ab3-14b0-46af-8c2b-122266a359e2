package minaloc.mbaza.api.location.seeds;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LocationSeedRunner implements CommandLineRunner {

    private final LocationSeed locationSeed;

    @Override
    public void run(String... args) throws Exception {
        locationSeed.seedLocations();
    }
}

