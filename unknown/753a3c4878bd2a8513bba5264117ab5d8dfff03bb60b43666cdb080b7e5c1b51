package minaloc.mbaza.api.location.mappers;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Cell;
import minaloc.mbaza.api.location.dtos.CellDTO;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CellMapper {

    private final VillageMapper villageMapper;

    public CellDTO map(Cell cell) {
        return new CellDTO(cell.getName(), cell.getCode());
    }

}