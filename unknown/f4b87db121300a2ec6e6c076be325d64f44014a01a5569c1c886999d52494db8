package minaloc.mbaza.api.location.usecases;

import lombok.AllArgsConstructor;
import minaloc.mbaza.api.location.domains.District;
import minaloc.mbaza.api.location.dtos.DistrictDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.DistrictMapper;
import minaloc.mbaza.api.location.repositories.DistrictRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class GetAllDistricts {

    private final DistrictRepository districtRepository;
    private final DistrictMapper districtMapper;

    public Page<DistrictDTO> execute(Pageable pageable) {
        Page<District> districtPage = districtRepository.findAll(pageable);
        if (districtPage.isEmpty()) {
            throw new LocationServiceException("No districts found");
        }

        try {
            List<DistrictDTO> districtDTOs = districtPage.getContent().stream()
                    .map(districtMapper::map)
                    .collect(Collectors.toList());
            return new PageImpl<>(districtDTOs, pageable, districtPage.getTotalElements());
        } catch (Exception e) {
            throw new LocationServiceException("Error mapping paginated districts ");
        }
    }
}
