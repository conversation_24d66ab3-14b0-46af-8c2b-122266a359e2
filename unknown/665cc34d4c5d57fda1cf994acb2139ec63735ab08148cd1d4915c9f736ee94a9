package minaloc.mbaza.api.location.seeds;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * This class defines the structure of the location data format for JSON deserialization.
 * It contains nested classes for Province, District, Sector, Cell, and Village.
 * Each class has fields for name and code, and the appropriate nested lists.
 */
public class LocationDataFormat {

    @Data
    @NoArgsConstructor
    public static class ProvinceDto {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        @JsonProperty("districts")
        private List<DistrictDto> districts;
    }

    @Data
    @NoArgsConstructor
    public static class DistrictDto {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        @JsonProperty("sectors")
        private List<SectorDto> sectors;
    }

    @Data
    @NoArgsConstructor
    public static class SectorDto {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        @JsonProperty("cells")
        private List<CellDto> cells;
    }

    @Data
    @NoArgsConstructor
    public static class CellDto {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;

        @JsonProperty("villages")
        private List<VillageDto> villages;
    }

    @Data
    @NoArgsConstructor
    public static class VillageDto {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;
    }
}