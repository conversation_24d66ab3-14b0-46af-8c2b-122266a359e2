package minaloc.mbaza.api.location.usecases;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.*;
import minaloc.mbaza.api.location.dtos.LocationHierarchyDTO;
import minaloc.mbaza.api.location.exceptions.LocationServiceException;
import minaloc.mbaza.api.location.mappers.*;
import minaloc.mbaza.api.location.repositories.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SearchLocationHierarchyUseCase {

    private final ProvinceRepository provinceRepository;
    private final DistrictRepository districtRepository;
    private final SectorRepository sectorRepository;
    private final CellRepository cellRepository;
    private final VillageRepository villageRepository;

    private final ProvinceMapper provinceMapper;
    private final SectorMapper sectorMapper;
    private final DistrictMapper districtMapper;
    private final CellMapper cellMapper;
    private final VillageMapper villageMapper;

    public List<LocationHierarchyDTO> execute(String locationName, String locationType) {
        if (locationName == null || locationName.trim().isEmpty()) {
            throw new LocationServiceException("Location name must be provided");
        }

        List<LocationHierarchyDTO> results = new ArrayList<>();

        // Search based on specified locationType
        if (locationType != null && !locationType.trim().isEmpty()) {
            switch (locationType.toUpperCase()) {
                case "VILLAGE" -> results.addAll(searchVillages(locationName));
                case "CELL" -> results.addAll(searchCells(locationName));
                case "SECTOR" -> results.addAll(searchSectors(locationName));
                case "DISTRICT" -> results.addAll(searchDistricts(locationName));
                case "PROVINCE" -> results.addAll(searchProvinces(locationName));
                default -> throw new LocationServiceException("Invalid location type: " + locationType);
            }
        } else {
            // Search across all levels
            results.addAll(searchVillages(locationName));
            results.addAll(searchCells(locationName));
            results.addAll(searchSectors(locationName));
            results.addAll(searchDistricts(locationName));
            results.addAll(searchProvinces(locationName));
        }


        return results;
    }

    private List<LocationHierarchyDTO> searchVillages(String name) {
        List<LocationHierarchyDTO> result = new ArrayList<>();
        List<Village> villages = villageRepository.findAllByNameIgnoreCase(name);

        for (Village village : villages) {
            Cell cell = village.getCell();
            Sector sector = cell.getSector();
            District district = sector.getDistrict();
            Province province = district.getProvince();

            result.add(LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("VILLAGE")
                    .village(villageMapper.map(village))
                    .cell(cellMapper.map(cell))
                    .sector(sectorMapper.map(sector))
                    .district(districtMapper.map(district))
                    .province(provinceMapper.map(province))
                    .hierarchyPath(String.format("%s > %s > %s > %s > %s",
                            province.getName(), district.getName(), sector.getName(), cell.getName(), village.getName()))
                    .build());
        }

        return result;
    }

    private List<LocationHierarchyDTO> searchCells(String name) {
        List<LocationHierarchyDTO> result = new ArrayList<>();
        List<Cell> cells = cellRepository.findAllByNameIgnoreCase(name);

        for (Cell cell : cells) {
            Sector sector = cell.getSector();
            District district = sector.getDistrict();
            Province province = district.getProvince();

            result.add(LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("CELL")
                    .cell(cellMapper.map(cell))
                    .sector(sectorMapper.map(sector))
                    .district(districtMapper.map(district))
                    .province(provinceMapper.map(province))
                    .hierarchyPath(String.format("%s > %s > %s > %s",
                            province.getName(), district.getName(), sector.getName(), cell.getName()))
                    .build());
        }

        return result;
    }

    private List<LocationHierarchyDTO> searchSectors(String name) {
        List<LocationHierarchyDTO> result = new ArrayList<>();
        List<Sector> sectors = sectorRepository.findAllByNameIgnoreCase(name);

        for (Sector sector : sectors) {
            District district = sector.getDistrict();
            Province province = district.getProvince();

            result.add(LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("SECTOR")
                    .sector(sectorMapper.map(sector))
                    .district(districtMapper.map(district))
                    .province(provinceMapper.map(province))
                    .hierarchyPath(String.format("%s > %s > %s",
                            province.getName(), district.getName(), sector.getName()))
                    .build());
        }

        return result;
    }

    private List<LocationHierarchyDTO> searchDistricts(String name) {
        List<LocationHierarchyDTO> result = new ArrayList<>();
        List<District> districts = districtRepository.findAllByNameIgnoreCase(name);

        for (District district : districts) {
            Province province = district.getProvince();

            result.add(LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("DISTRICT")
                    .district(districtMapper.map(district))
                    .province(provinceMapper.map(province))
                    .hierarchyPath(String.format("%s > %s", province.getName(), district.getName()))
                    .build());
        }

        return result;
    }

    private List<LocationHierarchyDTO> searchProvinces(String name) {
        List<LocationHierarchyDTO> result = new ArrayList<>();
        List<Province> provinces = provinceRepository.findAllByNameIgnoreCase(name);

        for (Province province : provinces) {
            result.add(LocationHierarchyDTO.builder()
                    .searchedLocation(name)
                    .searchedLocationType("PROVINCE")
                    .province(provinceMapper.map(province))
                    .hierarchyPath(province.getName())
                    .build());
        }

        return result;
    }
}
