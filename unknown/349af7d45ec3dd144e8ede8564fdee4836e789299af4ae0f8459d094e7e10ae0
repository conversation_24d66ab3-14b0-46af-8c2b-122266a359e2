package minaloc.mbaza.api.location.mappers;

import lombok.RequiredArgsConstructor;
import minaloc.mbaza.api.location.domains.Sector;
import minaloc.mbaza.api.location.dtos.SectorDTO;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SectorMapper {

    private final CellMapper cellMapper;

    public SectorDTO map(Sector sector) {
        return new SectorDTO(sector.getName(), sector.getCode());
    }

}