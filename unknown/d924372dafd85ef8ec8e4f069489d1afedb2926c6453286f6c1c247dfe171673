package minaloc.mbaza.api.location.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationHierarchyDTO {
    private ProvinceDTO province;
    private DistrictDTO district;
    private SectorDTO sector;
    private CellDTO cell;
    private VillageDTO village;
    private String searchedLocation;
    private String searchedLocationType;
    private String hierarchyPath;
}